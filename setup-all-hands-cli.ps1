# All-Hands CLI Setup Script
# Automated setup for All-Hands CLI mode in Windows/WSL environment

param(
    [string]$InstallMethod = "python",  # python, docker, or dev
    [string]$LLMModel = "anthropic/claude-sonnet-4-20250514",
    [string]$APIKey = "",
    [string]$WorkspaceDir = "z:\vscode",
    [switch]$SkipDocker,
    [switch]$Force
)

Write-Host "=== All-Hands CLI Setup ===" -ForegroundColor Cyan
Write-Host "Installation Method: $InstallMethod" -ForegroundColor Yellow
Write-Host "Workspace Directory: $WorkspaceDir" -ForegroundColor Yellow
Write-Host ""

# Function to check prerequisites
function Test-Prerequisites {
    Write-Host "Checking prerequisites..." -ForegroundColor Yellow
    
    # Check Python version
    try {
        $pythonVersion = python --version 2>$null
        if ($pythonVersion -match "Python (\d+)\.(\d+)") {
            $major = [int]$matches[1]
            $minor = [int]$matches[2]
            if ($major -eq 3 -and $minor -ge 12 -and $minor -lt 14) {
                Write-Host "✓ Python $pythonVersion found" -ForegroundColor Green
            } else {
                Write-Host "✗ Python 3.12-3.13 required, found $pythonVersion" -ForegroundColor Red
                return $false
            }
        }
    } catch {
        Write-Host "✗ Python not found or not accessible" -ForegroundColor Red
        return $false
    }
    
    # Check Docker (if not skipped)
    if (-not $SkipDocker) {
        try {
            $dockerVersion = docker --version 2>$null
            if ($dockerVersion) {
                Write-Host "✓ Docker found: $dockerVersion" -ForegroundColor Green
            }
        } catch {
            Write-Host "⚠ Docker not found - Docker installation will be limited" -ForegroundColor Yellow
        }
    }
    
    # Check WSL
    try {
        $wslVersion = wsl --version 2>$null
        if ($wslVersion) {
            Write-Host "✓ WSL available" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠ WSL not found - some features may be limited" -ForegroundColor Yellow
    }
    
    # Check workspace directory
    if (Test-Path $WorkspaceDir) {
        Write-Host "✓ Workspace directory exists: $WorkspaceDir" -ForegroundColor Green
    } else {
        Write-Host "⚠ Workspace directory not found: $WorkspaceDir" -ForegroundColor Yellow
        Write-Host "  Creating workspace directory..." -ForegroundColor Gray
        New-Item -ItemType Directory -Path $WorkspaceDir -Force | Out-Null
        Write-Host "✓ Workspace directory created" -ForegroundColor Green
    }
    
    return $true
}

# Function to install via Python
function Install-PythonMethod {
    Write-Host "`nInstalling All-Hands via Python..." -ForegroundColor Yellow
    
    try {
        # Install openhands-ai
        Write-Host "Installing openhands-ai package..." -ForegroundColor Gray
        pip install openhands-ai
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ OpenHands installed successfully" -ForegroundColor Green
            
            # Test installation
            Write-Host "Testing installation..." -ForegroundColor Gray
            $version = python -c "import openhands; print('OpenHands installed')" 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ Installation verified" -ForegroundColor Green
                return $true
            }
        }
    } catch {
        Write-Host "✗ Python installation failed: $_" -ForegroundColor Red
    }
    
    return $false
}

# Function to setup Docker method
function Setup-DockerMethod {
    Write-Host "`nSetting up Docker method..." -ForegroundColor Yellow
    
    # Create Docker run script
    $dockerScript = @"
#!/bin/bash
# All-Hands Docker Launch Script

# Set environment variables
export SANDBOX_VOLUMES="$WorkspaceDir:/workspace"
export LLM_MODEL="$LLMModel"
export LLM_API_KEY="$APIKey"

# Run Docker container
docker run -it \
    --pull=always \
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.45-nikolaik \
    -e SANDBOX_USER_ID=`$(id -u) \
    -e SANDBOX_VOLUMES=`$SANDBOX_VOLUMES \
    -e LLM_API_KEY=`$LLM_API_KEY \
    -e LLM_MODEL=`$LLM_MODEL \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -v ~/.openhands:/.openhands \
    --add-host host.docker.internal:host-gateway \
    --name openhands-app-`$(date +%Y%m%d%H%M%S) \
    docker.all-hands.dev/all-hands-ai/openhands:0.45 \
    python -m openhands.cli.main --override-cli-mode true
"@
    
    $dockerScript | Out-File -FilePath "$WorkspaceDir\launch-all-hands-docker.sh" -Encoding UTF8
    Write-Host "✓ Created Docker launch script: launch-all-hands-docker.sh" -ForegroundColor Green
    
    # Create PowerShell wrapper
    $psScript = @"
# All-Hands Docker Launch (PowerShell)
`$env:SANDBOX_VOLUMES = "$WorkspaceDir:/workspace"
`$env:LLM_MODEL = "$LLMModel"
`$env:LLM_API_KEY = "$APIKey"

docker run -it ``
    --pull=always ``
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.45-nikolaik ``
    -e SANDBOX_USER_ID=1000 ``
    -e SANDBOX_VOLUMES=`$env:SANDBOX_VOLUMES ``
    -e LLM_API_KEY=`$env:LLM_API_KEY ``
    -e LLM_MODEL=`$env:LLM_MODEL ``
    -v /var/run/docker.sock:/var/run/docker.sock ``
    -v ~/.openhands:/.openhands ``
    --add-host host.docker.internal:host-gateway ``
    --name openhands-app-`$(Get-Date -Format "yyyyMMddHHmmss") ``
    docker.all-hands.dev/all-hands-ai/openhands:0.45 ``
    python -m openhands.cli.main --override-cli-mode true
"@
    
    $psScript | Out-File -FilePath "$WorkspaceDir\launch-all-hands-docker.ps1" -Encoding UTF8
    Write-Host "✓ Created PowerShell Docker launch script: launch-all-hands-docker.ps1" -ForegroundColor Green
    
    return $true
}

# Function to create configuration files
function Create-Configuration {
    Write-Host "`nCreating configuration files..." -ForegroundColor Yellow
    
    # Create .env file
    $envContent = @"
# All-Hands Configuration
LLM_MODEL=$LLMModel
LLM_API_KEY=$APIKey
SANDBOX_VOLUMES=$WorkspaceDir:/workspace
SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.45-nikolaik
SANDBOX_USER_ID=1000
"@
    
    $envContent | Out-File -FilePath "$WorkspaceDir\.env" -Encoding UTF8
    Write-Host "✓ Created .env file" -ForegroundColor Green
    
    # Create config.toml
    $configDir = "$env:USERPROFILE\.openhands"
    if (-not (Test-Path $configDir)) {
        New-Item -ItemType Directory -Path $configDir -Force | Out-Null
    }
    
    $configContent = @"
[core]
workspace_base = "$WorkspaceDir"
persist_sandbox = true

[llm]
model = "$LLMModel"
api_key = "$APIKey"
temperature = 0.1
max_tokens = 4096

[agent]
memory_enabled = true
confirmation_mode = false

[sandbox]
runtime_container_image = "docker.all-hands.dev/all-hands-ai/runtime:0.45-nikolaik"
user_id = 1000
"@
    
    $configContent | Out-File -FilePath "$configDir\config.toml" -Encoding UTF8
    Write-Host "✓ Created config.toml in $configDir" -ForegroundColor Green
    
    return $true
}

# Function to create launch scripts
function Create-LaunchScripts {
    Write-Host "`nCreating launch scripts..." -ForegroundColor Yellow
    
    # Python launch script
    $pythonLaunch = @"
#!/bin/bash
# All-Hands Python Launch Script
cd "$WorkspaceDir"
source .env 2>/dev/null || true
openhands
"@
    
    $pythonLaunch | Out-File -FilePath "$WorkspaceDir\launch-all-hands.sh" -Encoding UTF8
    Write-Host "✓ Created Python launch script: launch-all-hands.sh" -ForegroundColor Green
    
    # PowerShell launch script
    $psLaunch = @"
# All-Hands PowerShell Launch Script
Set-Location "$WorkspaceDir"
if (Test-Path ".env") {
    Get-Content ".env" | ForEach-Object {
        if (`$_ -match "^([^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable(`$matches[1], `$matches[2], "Process")
        }
    }
}
openhands
"@
    
    $psLaunch | Out-File -FilePath "$WorkspaceDir\launch-all-hands.ps1" -Encoding UTF8
    Write-Host "✓ Created PowerShell launch script: launch-all-hands.ps1" -ForegroundColor Green
    
    return $true
}

# Main execution
try {
    if (-not (Test-Prerequisites)) {
        Write-Host "`nPrerequisites check failed. Please install required components." -ForegroundColor Red
        exit 1
    }
    
    # Get API key if not provided
    if (-not $APIKey) {
        $APIKey = Read-Host "Enter your LLM API key (or press Enter to configure later)"
    }
    
    # Install based on method
    $installSuccess = $false
    switch ($InstallMethod.ToLower()) {
        "python" {
            $installSuccess = Install-PythonMethod
        }
        "docker" {
            $installSuccess = Setup-DockerMethod
        }
        "dev" {
            Write-Host "Development setup requires manual Git clone and Poetry installation" -ForegroundColor Yellow
            Write-Host "See documentation for details" -ForegroundColor Gray
            $installSuccess = $true
        }
        default {
            Write-Host "Unknown installation method: $InstallMethod" -ForegroundColor Red
            exit 1
        }
    }
    
    if ($installSuccess) {
        # Create configuration and launch scripts
        Create-Configuration | Out-Null
        Create-LaunchScripts | Out-Null
        
        if ($InstallMethod -eq "docker") {
            Setup-DockerMethod | Out-Null
        }
        
        Write-Host "`n=== Setup Complete! ===" -ForegroundColor Green
        Write-Host "`nNext steps:" -ForegroundColor Yellow
        Write-Host "1. Review configuration in $WorkspaceDir\.env" -ForegroundColor White
        Write-Host "2. Set your API key if not already done" -ForegroundColor White
        
        if ($InstallMethod -eq "python") {
            Write-Host "3. Launch with: .\launch-all-hands.ps1" -ForegroundColor White
        } elseif ($InstallMethod -eq "docker") {
            Write-Host "3. Launch with: .\launch-all-hands-docker.ps1" -ForegroundColor White
        }
        
        Write-Host "4. Use /settings command to configure preferences" -ForegroundColor White
        Write-Host "5. Use /init command to initialize your repository" -ForegroundColor White
        
    } else {
        Write-Host "`nSetup failed. Check error messages above." -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "`nSetup failed with error: $_" -ForegroundColor Red
    exit 1
}
