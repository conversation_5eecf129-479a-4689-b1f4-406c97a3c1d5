# All-Hands CLI Quick Start Script
# One-command setup for All-Hands CLI mode

param(
    [string]$APIKey = "",
    [string]$Model = "anthropic/claude-sonnet-4-20250514",
    [switch]$Docker,
    [switch]$Ski<PERSON>Install,
    [switch]$Demo
)

Write-Host @"
╔══════════════════════════════════════════════════════════════╗
║                    All-Hands CLI Quick Start                 ║
║                                                              ║
║  This script will set up All-Hands CLI mode for AI-powered  ║
║  software engineering in your Windows/WSL environment.      ║
╚══════════════════════════════════════════════════════════════╝
"@ -ForegroundColor Cyan

Write-Host ""

# Check if running in correct directory
if (-not (Test-Path "fix-wsl-path-translation.ps1")) {
    Write-Host "⚠ Please run this script from your vscode workspace directory" -ForegroundColor Yellow
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Gray
    $continue = Read-Host "Continue anyway? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 1
    }
}

# Get API key if not provided
if (-not $APIKey -and -not $Demo) {
    Write-Host "API Key Setup:" -ForegroundColor Yellow
    Write-Host "You'll need an API key from one of these providers:" -ForegroundColor Gray
    Write-Host "  • Anthropic (Claude): https://console.anthropic.com/" -ForegroundColor Gray
    Write-Host "  • OpenAI (GPT): https://platform.openai.com/" -ForegroundColor Gray
    Write-Host "  • Or use local models with Ollama" -ForegroundColor Gray
    Write-Host ""
    
    $APIKey = Read-Host "Enter your API key (or press Enter for demo mode)"
    if (-not $APIKey) {
        $Demo = $true
        Write-Host "Demo mode enabled - you'll need to configure API key later" -ForegroundColor Yellow
    }
}

# Step 1: Prerequisites check
Write-Host "Step 1: Checking prerequisites..." -ForegroundColor Green

$pythonOK = $false
try {
    $pythonVersion = python --version 2>$null
    if ($pythonVersion -match "Python (\d+)\.(\d+)") {
        $major = [int]$matches[1]
        $minor = [int]$matches[2]
        if ($major -eq 3 -and $minor -ge 12 -and $minor -lt 14) {
            Write-Host "✓ Python $pythonVersion" -ForegroundColor Green
            $pythonOK = $true
        } else {
            Write-Host "✗ Need Python 3.12-3.13, found $pythonVersion" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "✗ Python not found" -ForegroundColor Red
}

$dockerOK = $false
if ($Docker) {
    try {
        $dockerVersion = docker --version 2>$null
        if ($dockerVersion) {
            Write-Host "✓ Docker available" -ForegroundColor Green
            $dockerOK = $true
        }
    } catch {
        Write-Host "✗ Docker not found" -ForegroundColor Red
    }
}

# Step 2: Installation
if (-not $SkipInstall) {
    Write-Host "`nStep 2: Installing All-Hands..." -ForegroundColor Green
    
    if ($Docker -and $dockerOK) {
        Write-Host "Using Docker installation method..." -ForegroundColor Yellow
        # Docker setup will be handled in configuration step
        Write-Host "✓ Docker method selected" -ForegroundColor Green
    } elseif ($pythonOK) {
        Write-Host "Installing via pip..." -ForegroundColor Yellow
        try {
            pip install openhands-ai --quiet
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ All-Hands installed successfully" -ForegroundColor Green
            } else {
                Write-Host "✗ Installation failed" -ForegroundColor Red
                exit 1
            }
        } catch {
            Write-Host "✗ Installation failed: $_" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "✗ Cannot install - missing prerequisites" -ForegroundColor Red
        Write-Host "Please install Python 3.12+ or Docker first" -ForegroundColor Yellow
        exit 1
    }
} else {
    Write-Host "`nStep 2: Skipping installation (--SkipInstall specified)" -ForegroundColor Yellow
}

# Step 3: Configuration
Write-Host "`nStep 3: Creating configuration..." -ForegroundColor Green

$workspaceDir = Get-Location
$configDir = "$env:USERPROFILE\.openhands"

# Create config directory
if (-not (Test-Path $configDir)) {
    New-Item -ItemType Directory -Path $configDir -Force | Out-Null
}

# Create .env file
$envContent = @"
# All-Hands Configuration
LLM_MODEL=$Model
LLM_API_KEY=$APIKey
SANDBOX_VOLUMES=$workspaceDir:/workspace
SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.45-nikolaik
SANDBOX_USER_ID=1000
"@

$envContent | Out-File -FilePath "$workspaceDir\.env" -Encoding UTF8
Write-Host "✓ Created .env configuration" -ForegroundColor Green

# Create config.toml
$configContent = @"
[core]
workspace_base = "$workspaceDir"
persist_sandbox = true

[llm]
model = "$Model"
api_key = "$APIKey"
temperature = 0.1
max_tokens = 4096

[agent]
memory_enabled = true
confirmation_mode = false

[sandbox]
runtime_container_image = "docker.all-hands.dev/all-hands-ai/runtime:0.45-nikolaik"
user_id = 1000
"@

$configContent | Out-File -FilePath "$configDir\config.toml" -Encoding UTF8
Write-Host "✓ Created config.toml" -ForegroundColor Green

# Step 4: Create launch scripts
Write-Host "`nStep 4: Creating launch scripts..." -ForegroundColor Green

if ($Docker -and $dockerOK) {
    # Docker launch script
    $dockerLaunch = @"
# All-Hands Docker Launch
`$env:SANDBOX_VOLUMES = "$workspaceDir:/workspace"
`$env:LLM_MODEL = "$Model"
`$env:LLM_API_KEY = "$APIKey"

Write-Host "Launching All-Hands in Docker..." -ForegroundColor Green
docker run -it ``
    --pull=always ``
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.45-nikolaik ``
    -e SANDBOX_USER_ID=1000 ``
    -e SANDBOX_VOLUMES=`$env:SANDBOX_VOLUMES ``
    -e LLM_API_KEY=`$env:LLM_API_KEY ``
    -e LLM_MODEL=`$env:LLM_MODEL ``
    -v /var/run/docker.sock:/var/run/docker.sock ``
    -v ~/.openhands:/.openhands ``
    --add-host host.docker.internal:host-gateway ``
    --name openhands-app-`$(Get-Date -Format "yyyyMMddHHmmss") ``
    docker.all-hands.dev/all-hands-ai/openhands:0.45 ``
    python -m openhands.cli.main --override-cli-mode true
"@
    
    $dockerLaunch | Out-File -FilePath "$workspaceDir\start-all-hands.ps1" -Encoding UTF8
    Write-Host "✓ Created Docker launch script: start-all-hands.ps1" -ForegroundColor Green
} else {
    # Python launch script
    $pythonLaunch = @"
# All-Hands Python Launch
Set-Location "$workspaceDir"

# Load environment variables
if (Test-Path ".env") {
    Get-Content ".env" | ForEach-Object {
        if (`$_ -match "^([^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable(`$matches[1], `$matches[2], "Process")
        }
    }
}

Write-Host "Launching All-Hands CLI..." -ForegroundColor Green
openhands
"@
    
    $pythonLaunch | Out-File -FilePath "$workspaceDir\start-all-hands.ps1" -Encoding UTF8
    Write-Host "✓ Created Python launch script: start-all-hands.ps1" -ForegroundColor Green
}

# Step 5: Create helper scripts
Write-Host "`nStep 5: Creating helper scripts..." -ForegroundColor Green

# Quick commands script
$quickCommands = @"
# All-Hands Quick Commands

Write-Host "All-Hands Quick Commands:" -ForegroundColor Cyan
Write-Host ""
Write-Host "Basic Commands:" -ForegroundColor Yellow
Write-Host "  /help     - Show all available commands"
Write-Host "  /init     - Initialize repository for AI exploration"
Write-Host "  /status   - Show conversation details and metrics"
Write-Host "  /settings - Configure model and preferences"
Write-Host "  /new      - Start a new conversation"
Write-Host "  /exit     - Exit All-Hands"
Write-Host ""
Write-Host "Advanced Commands:" -ForegroundColor Yellow
Write-Host "  /resume   - Resume paused agent"
Write-Host "  Ctrl-P    - Pause running agent"
Write-Host ""
Write-Host "Example Tasks:" -ForegroundColor Yellow
Write-Host "  'Analyze this codebase and suggest improvements'"
Write-Host "  'Create unit tests for the main functions'"
Write-Host "  'Set up a CI/CD pipeline for this project'"
Write-Host "  'Generate documentation for the API'"
Write-Host "  'Optimize the database queries in this module'"
Write-Host ""
"@

$quickCommands | Out-File -FilePath "$workspaceDir\all-hands-commands.ps1" -Encoding UTF8
Write-Host "✓ Created quick commands reference: all-hands-commands.ps1" -ForegroundColor Green

# Step 6: Final setup
Write-Host "`nStep 6: Final setup..." -ForegroundColor Green

# Add to .gitignore if it exists
if (Test-Path ".gitignore") {
    $gitignoreContent = Get-Content ".gitignore" -Raw
    if ($gitignoreContent -notmatch "\.env") {
        Add-Content ".gitignore" "`n# All-Hands configuration`n.env`n.openhands/"
        Write-Host "✓ Updated .gitignore" -ForegroundColor Green
    }
} else {
    @"
# All-Hands configuration
.env
.openhands/
"@ | Out-File -FilePath ".gitignore" -Encoding UTF8
    Write-Host "✓ Created .gitignore" -ForegroundColor Green
}

# Success message
Write-Host @"

╔══════════════════════════════════════════════════════════════╗
║                        Setup Complete!                       ║
╚══════════════════════════════════════════════════════════════╝

🚀 All-Hands CLI is ready to use!

Quick Start:
  1. Run: .\start-all-hands.ps1
  2. Use: /init to initialize your repository
  3. Try: "Analyze this codebase and suggest improvements"

Configuration Files:
  • .env - Environment variables
  • ~/.openhands/config.toml - Main configuration
  • start-all-hands.ps1 - Launch script

Helpful Commands:
  • .\all-hands-commands.ps1 - Show command reference
  • Get-Content .env - View current configuration

Documentation:
  • all-hands-cli-setup.md - Detailed setup guide
  • all-hands-enhancements.md - Advanced features

"@ -ForegroundColor Green

if ($Demo) {
    Write-Host "⚠ Demo Mode: Remember to set your API key in .env file" -ForegroundColor Yellow
}

Write-Host "Happy coding with AI assistance! 🤖✨" -ForegroundColor Cyan
