# Logging Configuration for OpenHands CLI
# This file configures structured logging with rotation and monitoring

version: 1
disable_existing_loggers: false

# Formatters define the log message format
formatters:
  standard:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(lineno)d - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  json:
    format: '{"timestamp": "%(asctime)s", "logger": "%(name)s", "level": "%(levelname)s", "module": "%(module)s", "function": "%(funcName)s", "line": %(lineno)d, "message": "%(message)s"}'
    datefmt: '%Y-%m-%dT%H:%M:%S'
  
  security:
    format: '[SECURITY] %(asctime)s - %(levelname)s - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'

# Handlers define where logs are sent
handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout
  
  file_info:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: detailed
    filename: ./logs/openhands.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8
  
  file_error:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: detailed
    filename: ./logs/openhands_error.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8
  
  file_debug:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: json
    filename: ./logs/openhands_debug.log
    maxBytes: 52428800  # 50MB
    backupCount: 3
    encoding: utf8
  
  security_log:
    class: logging.handlers.RotatingFileHandler
    level: WARNING
    formatter: security
    filename: ./logs/security.log
    maxBytes: 10485760  # 10MB
    backupCount: 10
    encoding: utf8
  
  performance_log:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: json
    filename: ./logs/performance.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8

# Loggers define which handlers to use for different components
loggers:
  openhands:
    level: INFO
    handlers: [console, file_info, file_error]
    propagate: false
  
  openhands.security:
    level: WARNING
    handlers: [security_log, console]
    propagate: false
  
  openhands.performance:
    level: INFO
    handlers: [performance_log]
    propagate: false
  
  openhands.agent:
    level: DEBUG
    handlers: [file_debug, console]
    propagate: false
  
  openhands.sandbox:
    level: INFO
    handlers: [file_info, console]
    propagate: false
  
  docker:
    level: WARNING
    handlers: [file_error]
    propagate: false
  
  urllib3:
    level: WARNING
    handlers: [file_error]
    propagate: false
  
  requests:
    level: WARNING
    handlers: [file_error]
    propagate: false

# Root logger configuration
root:
  level: INFO
  handlers: [console, file_info]

# Additional configuration for specific environments
development:
  loggers:
    openhands:
      level: DEBUG
      handlers: [console, file_debug]
    
    root:
      level: DEBUG

production:
  loggers:
    openhands:
      level: INFO
      handlers: [file_info, file_error]
    
    root:
      level: WARNING
      handlers: [file_error]

# Log rotation and cleanup settings
rotation:
  # Automatic cleanup of old log files
  cleanup:
    enabled: true
    max_age_days: 30
    max_total_size: "1GB"
  
  # Compression of old log files
  compression:
    enabled: true
    algorithm: "gzip"
    
  # Archive settings
  archive:
    enabled: true
    location: "./logs/archive"
    retention_days: 90
