# Claude Code Setup Script for WSL
# This script sets up Claude Code in WSL environment

Write-Host "Setting up Claude Code in WSL..." -ForegroundColor Green

# Check if WSL is available
Write-Host "Checking WSL availability..." -ForegroundColor Yellow
$wslCheck = wsl --list --verbose
if ($LASTEXITCODE -ne 0) {
    Write-Host "WSL is not available. Please install WSL first." -ForegroundColor Red
    exit 1
}

Write-Host "WSL is available. Proceeding with setup..." -ForegroundColor Green

# Step 1: Install NVM in WSL
Write-Host "Installing NVM (Node Version Manager)..." -ForegroundColor Yellow
wsl -d Ubuntu-22.04 -- bash -c "curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash"

# Step 2: Source NVM and install Node.js 18
Write-Host "Installing Node.js 18..." -ForegroundColor Yellow
wsl -d Ubuntu-22.04 -- bash -c "source ~/.bashrc && nvm install 18 && nvm use 18 && nvm alias default 18"

# Step 3: Verify Node.js installation
Write-Host "Verifying Node.js installation..." -ForegroundColor Yellow
$nodeVersion = wsl -d Ubuntu-22.04 -- bash -c "source ~/.bashrc && node --version"
Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green

# Step 4: Install Claude Code
Write-Host "Installing Claude Code..." -ForegroundColor Yellow
wsl -d Ubuntu-22.04 -- bash -c "source ~/.bashrc && npm install -g @anthropic-ai/claude-code"

# Step 5: Verify Claude Code installation
Write-Host "Verifying Claude Code installation..." -ForegroundColor Yellow
$claudeVersion = wsl -d Ubuntu-22.04 -- bash -c "source ~/.bashrc && claude --version"
Write-Host "Claude Code version: $claudeVersion" -ForegroundColor Green

Write-Host "Claude Code setup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "To use Claude Code:" -ForegroundColor Cyan
Write-Host "1. Open WSL terminal: wsl -d Ubuntu-22.04" -ForegroundColor White
Write-Host "2. Navigate to your project directory" -ForegroundColor White
Write-Host "3. Run: claude" -ForegroundColor White
Write-Host ""
Write-Host "Note: You'll need to authenticate with your Anthropic API key on first use." -ForegroundColor Yellow
