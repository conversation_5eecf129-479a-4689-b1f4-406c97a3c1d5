@echo off
echo 🦙 Starting OpenHands with Ollama
echo ================================

REM Set environment variables
set LLM_MODEL=ollama/qwen2.5-coder:7b
set LLM_BASE_URL=http://************:11434
set LLM_API_KEY=ollama
set PORT=3001
set HOST=0.0.0.0

echo 🔧 Environment configured:
echo    LLM_MODEL=%LLM_MODEL%
echo    LLM_BASE_URL=%LLM_BASE_URL%
echo    PORT=%PORT%

echo.
echo 🚀 Starting OpenHands server...
echo 🌐 Access at: http://localhost:%PORT%
echo 🛑 Press Ctrl+C to stop
echo.

REM Try to start with uvicorn
uvicorn openhands.server.app:app --host %HOST% --port %PORT%

pause
