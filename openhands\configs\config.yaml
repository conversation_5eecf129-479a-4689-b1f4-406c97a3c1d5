# OpenHands Configuration File
# This file contains the main configuration for OpenHands CLI

# =============================================================================
# CORE SETTINGS
# =============================================================================
core:
  workspace_dir: "./workspaces"
  log_level: "INFO"
  log_dir: "./logs"
  max_concurrent_tasks: 3
  request_timeout: 300

# =============================================================================
# LLM CONFIGURATION
# =============================================================================
llm:
  # Default model configuration
  default_model: "gpt-4"
  
  # Model-specific settings
  models:
    gpt-4:
      provider: "openai"
      max_tokens: 4096
      temperature: 0.1
      timeout: 60
    
    gpt-3.5-turbo:
      provider: "openai"
      max_tokens: 4096
      temperature: 0.1
      timeout: 30
    
    claude-3-sonnet:
      provider: "anthropic"
      max_tokens: 4096
      temperature: 0.1
      timeout: 60
    
    llama2:
      provider: "ollama"
      max_tokens: 4096
      temperature: 0.1
      timeout: 120

# =============================================================================
# SANDBOX CONFIGURATION
# =============================================================================
sandbox:
  enabled: true
  container_image: "ghcr.io/all-hands-ai/runtime:0.9-nikolaik"
  memory_limit: "4g"
  cpu_limit: 2
  network_mode: "bridge"
  
  # Security settings
  security:
    read_only_root: true
    no_new_privileges: true
    drop_capabilities: ["ALL"]
    add_capabilities: ["CHOWN", "DAC_OVERRIDE", "SETUID", "SETGID"]

# =============================================================================
# AGENT CONFIGURATION
# =============================================================================
agent:
  # Default agent settings
  default_agent: "CodeActAgent"
  
  # Agent-specific configurations
  agents:
    CodeActAgent:
      max_iterations: 20
      enable_auto_lint: true
      enable_auto_test: true
    
    PlannerAgent:
      max_planning_steps: 10
      enable_verification: true

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
security:
  jwt_secret_key: "change-this-in-production"
  allowed_hosts: ["localhost", "127.0.0.1"]
  cors_origins: ["http://localhost:3000", "http://127.0.0.1:3000"]
  
  # File access restrictions
  file_access:
    allowed_extensions: [".py", ".js", ".ts", ".html", ".css", ".md", ".txt", ".json", ".yaml", ".yml"]
    blocked_paths: ["/etc", "/proc", "/sys", "/dev"]
    max_file_size: "10MB"

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
monitoring:
  enabled: true
  metrics_port: 8080
  health_check_interval: 30
  
  # Telemetry settings
  telemetry:
    enabled: false
    endpoint: "https://telemetry.openhands.ai"
    sample_rate: 0.1

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
performance:
  # Memory management
  memory:
    workspace_limit: "1GB"
    agent_limit: "512MB"
    garbage_collection_interval: 300
  
  # Caching
  cache:
    enabled: true
    ttl: 3600
    max_size: "100MB"
  
  # Concurrency
  concurrency:
    max_workers: 4
    queue_size: 100

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
backup:
  enabled: true
  directory: "./backups"
  retention_days: 30
  auto_backup_interval: 24
  
  # What to backup
  include:
    - "workspaces"
    - "configs"
    - "logs"
  
  # What to exclude
  exclude:
    - "*.tmp"
    - "*.log"
    - "__pycache__"
    - ".git"

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
development:
  debug: false
  hot_reload: false
  profiling: false
  
  # Development server
  server:
    host: "localhost"
    port: 3000
    auto_reload: false
