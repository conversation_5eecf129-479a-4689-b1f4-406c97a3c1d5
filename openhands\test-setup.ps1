# OpenHands + Ollama Setup Test Script
# This script performs a comprehensive test of the setup

Write-Host "🧪 OpenHands + Ollama Setup Test" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

$testResults = @()

# Test 1: Python and OpenHands Installation
Write-Host "`n1️⃣  Testing Python and OpenHands installation..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    $testResults += @{Test="Python"; Status="✅ PASS"; Details=$pythonVersion}
    Write-Host "   ✅ Python: $pythonVersion" -ForegroundColor Green
    
    $openhandsTest = python -c "import openhands; print(f'OpenHands {openhands.__version__}')" 2>&1
    $testResults += @{Test="OpenHands Import"; Status="✅ PASS"; Details=$openhandsTest}
    Write-Host "   ✅ OpenHands: $openhandsTest" -ForegroundColor Green
} catch {
    $testResults += @{Test="Python/OpenHands"; Status="❌ FAIL"; Details=$_.Exception.Message}
    Write-Host "   ❌ Python/OpenHands test failed" -ForegroundColor Red
}

# Test 2: Ollama Server Connection
Write-Host "`n2️⃣  Testing Ollama server connection..." -ForegroundColor Yellow
try {
    $ollamaResponse = Invoke-RestMethod -Uri "http://************:11434/api/tags" -Method GET -TimeoutSec 10
    $modelCount = $ollamaResponse.models.Count
    $testResults += @{Test="Ollama Connection"; Status="✅ PASS"; Details="$modelCount models available"}
    Write-Host "   ✅ Ollama server accessible with $modelCount models" -ForegroundColor Green
    
    # Check if our configured model exists
    $configuredModel = "qwen2.5-coder:7b"
    $modelExists = $ollamaResponse.models | Where-Object { $_.name -eq $configuredModel }
    if ($modelExists) {
        $testResults += @{Test="Model Availability"; Status="✅ PASS"; Details="$configuredModel found"}
        Write-Host "   ✅ Configured model '$configuredModel' is available" -ForegroundColor Green
    } else {
        $testResults += @{Test="Model Availability"; Status="⚠️  WARN"; Details="$configuredModel not found"}
        Write-Host "   ⚠️  Configured model '$configuredModel' not found on server" -ForegroundColor Yellow
    }
} catch {
    $testResults += @{Test="Ollama Connection"; Status="❌ FAIL"; Details=$_.Exception.Message}
    Write-Host "   ❌ Cannot connect to Ollama server" -ForegroundColor Red
}

# Test 3: Configuration Files
Write-Host "`n3️⃣  Testing configuration files..." -ForegroundColor Yellow
$configFiles = @(
    @{Path="openhands\configs\.env"; Name="Environment Config"},
    @{Path="openhands\start-remote-ollama.ps1"; Name="Startup Script"},
    @{Path="openhands\scripts\configure-remote-ollama.ps1"; Name="Configuration Script"}
)

foreach ($config in $configFiles) {
    if (Test-Path $config.Path) {
        $testResults += @{Test=$config.Name; Status="✅ PASS"; Details="File exists"}
        Write-Host "   ✅ $($config.Name): Found" -ForegroundColor Green
    } else {
        $testResults += @{Test=$config.Name; Status="❌ FAIL"; Details="File missing"}
        Write-Host "   ❌ $($config.Name): Missing" -ForegroundColor Red
    }
}

# Test 4: Environment Configuration
Write-Host "`n4️⃣  Testing environment configuration..." -ForegroundColor Yellow
if (Test-Path "openhands\configs\.env") {
    $envContent = Get-Content "openhands\configs\.env"
    
    # Check key configuration values
    $llmModel = $envContent | Select-String "^LLM_MODEL=" | ForEach-Object { $_.Line.Split('=')[1] }
    $llmBaseUrl = $envContent | Select-String "^LLM_BASE_URL=" | ForEach-Object { $_.Line.Split('=')[1] }
    $ollamaHost = $envContent | Select-String "^OLLAMA_HOST=" | ForEach-Object { $_.Line.Split('=')[1] }
    
    if ($llmModel) {
        $testResults += @{Test="LLM Model Config"; Status="✅ PASS"; Details=$llmModel}
        Write-Host "   ✅ LLM Model: $llmModel" -ForegroundColor Green
    }
    
    if ($llmBaseUrl) {
        $testResults += @{Test="LLM Base URL"; Status="✅ PASS"; Details=$llmBaseUrl}
        Write-Host "   ✅ LLM Base URL: $llmBaseUrl" -ForegroundColor Green
    }
    
    if ($ollamaHost) {
        $testResults += @{Test="Ollama Host"; Status="✅ PASS"; Details=$ollamaHost}
        Write-Host "   ✅ Ollama Host: $ollamaHost" -ForegroundColor Green
    }
} else {
    $testResults += @{Test="Environment Config"; Status="❌ FAIL"; Details="Config file missing"}
    Write-Host "   ❌ Environment configuration file missing" -ForegroundColor Red
}

# Test 5: Simple Model Inference (Optional)
Write-Host "`n5️⃣  Testing model inference (optional)..." -ForegroundColor Yellow
try {
    $testPrompt = @{
        model = "qwen2.5-coder:7b"
        prompt = "Say 'Hello from Ollama!'"
        stream = $false
    } | ConvertTo-Json
    
    Write-Host "   🔄 Sending test prompt to model (this may take a moment)..." -ForegroundColor Yellow
    $response = Invoke-RestMethod -Uri "http://************:11434/api/generate" -Method POST -Body $testPrompt -ContentType "application/json" -TimeoutSec 30
    
    if ($response.response) {
        $testResults += @{Test="Model Inference"; Status="✅ PASS"; Details="Model responded successfully"}
        Write-Host "   ✅ Model inference successful" -ForegroundColor Green
        Write-Host "   📝 Response: $($response.response.Trim())" -ForegroundColor Cyan
    } else {
        $testResults += @{Test="Model Inference"; Status="⚠️  WARN"; Details="Empty response"}
        Write-Host "   ⚠️  Model responded but with empty response" -ForegroundColor Yellow
    }
} catch {
    $testResults += @{Test="Model Inference"; Status="⚠️  WARN"; Details="Timeout or error (normal for first request)"}
    Write-Host "   ⚠️  Model inference test failed (this is normal for first request)" -ForegroundColor Yellow
    Write-Host "   📝 Error: $($_.Exception.Message)" -ForegroundColor Gray
}

# Test Summary
Write-Host "`n📊 Test Summary" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan

$passCount = ($testResults | Where-Object { $_.Status -like "*PASS*" }).Count
$failCount = ($testResults | Where-Object { $_.Status -like "*FAIL*" }).Count
$warnCount = ($testResults | Where-Object { $_.Status -like "*WARN*" }).Count
$totalTests = $testResults.Count

Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "✅ Passed: $passCount" -ForegroundColor Green
Write-Host "❌ Failed: $failCount" -ForegroundColor Red
Write-Host "⚠️  Warnings: $warnCount" -ForegroundColor Yellow

# Detailed Results
Write-Host "`n📋 Detailed Results:" -ForegroundColor Cyan
foreach ($result in $testResults) {
    Write-Host "  $($result.Status) $($result.Test): $($result.Details)" -ForegroundColor White
}

# Recommendations
Write-Host "`n💡 Recommendations:" -ForegroundColor Cyan
if ($failCount -eq 0) {
    Write-Host "🎉 All critical tests passed! Your OpenHands + Ollama setup is ready." -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Start OpenHands: .\openhands\start-remote-ollama.ps1" -ForegroundColor White
    Write-Host "2. Try a simple task to test the integration" -ForegroundColor White
} else {
    Write-Host "🔧 Some tests failed. Please address the issues above before proceeding." -ForegroundColor Red
    
    if ($testResults | Where-Object { $_.Test -eq "Ollama Connection" -and $_.Status -like "*FAIL*" }) {
        Write-Host ""
        Write-Host "Ollama Connection Issues:" -ForegroundColor Yellow
        Write-Host "- Ensure Ollama server is running on ************:11434" -ForegroundColor White
        Write-Host "- Check firewall settings" -ForegroundColor White
        Write-Host "- Verify network connectivity" -ForegroundColor White
    }
    
    if ($testResults | Where-Object { $_.Test -like "*Config*" -and $_.Status -like "*FAIL*" }) {
        Write-Host ""
        Write-Host "Configuration Issues:" -ForegroundColor Yellow
        Write-Host "- Run: .\openhands\scripts\configure-remote-ollama.ps1" -ForegroundColor White
    }
}

Write-Host "`n🏁 Test completed!" -ForegroundColor Green
