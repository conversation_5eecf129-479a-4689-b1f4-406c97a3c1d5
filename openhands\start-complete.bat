@echo off
echo 🦙 OpenHands Complete Startup (with Runtime)
echo ============================================

REM Clean up any existing containers
docker stop openhands-test 2>nul
docker rm openhands-test 2>nul

REM Set environment variables
set LLM_MODEL=ollama/qwen2.5-coder:7b
set LLM_BASE_URL=http://************:11434
set LLM_API_KEY=ollama

echo 🔧 Configuration:
echo   LLM_MODEL=%LLM_MODEL%
echo   LLM_BASE_URL=%LLM_BASE_URL%
echo   Port: 3001

echo.
echo 🚀 Starting OpenHands with runtime support...
echo 🌐 Access at: http://localhost:3001
echo ⏳ Runtime startup may take 1-2 minutes...

REM Create data directory
if not exist "%USERPROFILE%\.openhands" mkdir "%USERPROFILE%\.openhands"

REM Start with complete configuration including runtime
docker run -d --rm ^
    --name openhands-test ^
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.45-nikolaik ^
    -e LOG_ALL_EVENTS=true ^
    -e LLM_MODEL=%LLM_MODEL% ^
    -e LLM_BASE_URL=%LLM_BASE_URL% ^
    -e LLM_API_KEY=%LLM_API_KEY% ^
    -v /var/run/docker.sock:/var/run/docker.sock ^
    -v "%USERPROFILE%\.openhands":/home/<USER>/.openhands ^
    -p 3001:3000 ^
    --add-host host.docker.internal:host-gateway ^
    docker.all-hands.dev/all-hands-ai/openhands:0.45

if %errorlevel%==0 (
    echo ✅ OpenHands started successfully
    echo 🌐 Access at: http://localhost:3001
    echo 📋 Check logs: docker logs openhands-test -f
    echo 🛑 Stop with: docker stop openhands-test
) else (
    echo ❌ Failed to start OpenHands
)

echo.
echo 💡 If you see "Waiting for runtime to start..." this is normal.
echo    The runtime container is being downloaded and started.
echo    This can take 2-3 minutes on first run.

pause
