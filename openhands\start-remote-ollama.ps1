# OpenHands Remote Ollama Startup Script
Write-Host "🦙 Starting OpenHands with Remote Ollama..." -ForegroundColor Cyan

# Test Ollama connection
Write-Host "🔍 Testing connection to Ollama server..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://************:11434/api/tags" -Method GET -TimeoutSec 10
    Write-Host "✅ Ollama server is accessible" -ForegroundColor Green
}
catch {
    Write-Host "❌ Cannot connect to Ollama server at ************:11434" -ForegroundColor Red
    Write-Host "   Please check network connectivity and server status" -ForegroundColor Yellow
    exit 1
}

# Load environment variables
if (Test-Path "openhands\configs\.env") {
    Write-Host "📋 Loading configuration..." -ForegroundColor Yellow
    Get-Content "openhands\configs\.env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
    Write-Host "✅ Environment variables loaded" -ForegroundColor Green
}
else {
    Write-Warning "⚠️  .env file not found. Please run configuration first."
    exit 1
}

# Start OpenHands
Write-Host "🚀 Starting OpenHands with remote Ollama..." -ForegroundColor Green

# Try different ways to start OpenHands
try {
    # Method 1: Try the main CLI entry point
    python -m openhands $args
}
catch {
    try {
        # Method 2: Try direct openhands command
        openhands $args
    }
    catch {
        try {
            # Method 3: Try with explicit python path
            python -c "import openhands; from openhands.core.main import main; main()" $args
        }
        catch {
            Write-Host "❌ Could not start OpenHands. Please check the installation." -ForegroundColor Red
            Write-Host "Try running: pip install openhands-ai --upgrade" -ForegroundColor Yellow
            exit 1
        }
    }
}
