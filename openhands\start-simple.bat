@echo off
echo 🦙 OpenHands Simple Startup
echo ===========================

REM Clean up any existing containers
docker stop openhands-app 2>nul
docker rm openhands-app 2>nul

REM Set basic environment
set LLM_MODEL=ollama/qwen2.5-coder:7b
set LLM_BASE_URL=http://************:11434
set LLM_API_KEY=ollama

echo 🔧 Starting OpenHands...
echo 🌐 Will be available at: http://localhost:3000

REM Create data directory
if not exist "%USERPROFILE%\.openhands" mkdir "%USERPROFILE%\.openhands"

REM Simple Docker run command
docker run -d --rm ^
    -e LLM_MODEL=%LLM_MODEL% ^
    -e LLM_BASE_URL=%LLM_BASE_URL% ^
    -e LLM_API_KEY=%LLM_API_KEY% ^
    -v /var/run/docker.sock:/var/run/docker.sock ^
    -v "%USERPROFILE%\.openhands":/home/<USER>/.openhands ^
    -p 3000:3000 ^
    --name openhands-app ^
    docker.all-hands.dev/all-hands-ai/openhands:0.45

echo ✅ OpenHands started in background
echo 🌐 Access at: http://localhost:3000
echo 📋 Check logs: docker logs openhands-app
echo 🛑 Stop with: docker stop openhands-app

pause
