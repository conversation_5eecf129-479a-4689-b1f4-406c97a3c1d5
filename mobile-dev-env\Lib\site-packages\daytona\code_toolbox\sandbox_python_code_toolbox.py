# Copyright 2025 Daytona Platforms Inc.
# SPDX-License-Identifier: Apache-2.0

import base64
import re
from typing import Optional

from ..common.process import CodeRunParams


class SandboxPythonCodeToolbox:
    def get_run_command(self, code: str, params: Optional[CodeRunParams] = None) -> str:
        # Encode the provided code in base64
        base64_code = base64.b64encode(code.encode()).decode()

        # Override plt.show() method if matplotlib is imported
        if self._is_matplotlib_imported(code):
            code_wrapper = base64.b64decode(PYTHON_CODE_WRAPPER.encode()).decode()
            code_wrapper = code_wrapper.replace("{encoded_code}", base64_code)
            base64_code = base64.b64encode(code_wrapper.encode()).decode()

        # Build command-line arguments string
        argv = ""
        if params and params.argv:
            argv = " ".join(params.argv)

        # Execute the bootstrapper code directly
        # Use -u flag to ensure unbuffered output for real-time error reporting
        return (
            f""" sh -c 'python3 -u -c "exec(__import__(\\\"base64\\\")"""
            f""".b64decode(\\\"{base64_code}\\\").decode())" {argv}' """
        )

    @staticmethod
    def _is_matplotlib_imported(code: str) -> bool:
        """Simplified version that only uses regex to check for matplotlib imports"""
        patterns = [
            # Standard imports
            r"^[^#]*import\s+matplotlib",
            r"^[^#]*from\s+matplotlib",
            # Dynamic imports
            r'^[^#]*__import__\s*\(\s*[\'"]matplotlib[\'"]',
            r'^[^#]*importlib\.import_module\s*\(\s*[\'"]matplotlib[\'"]',
            # Other dynamic loading patterns
            r'^[^#]*loader\.load_module\s*\(\s*[\'"]matplotlib[\'"]',
            r'^[^#]*sys\.modules\[[\'"]matplotlib[\'"]\]',
        ]

        for pattern in patterns:
            if re.search(pattern, code, re.MULTILINE):
                return True

        return False


PYTHON_CODE_WRAPPER = """
aW1wb3J0IGJhc2U2NAppbXBvcnQgZGF0ZXRpbWUKaW1wb3J0IGhhc2hsaWIKaW1wb3J0IGlvCmlt
cG9ydCBqc29uCmltcG9ydCBsaW5lY2FjaGUKaW1wb3J0IHN5cwppbXBvcnQgdHJhY2ViYWNrCmlt
cG9ydCB0eXBlcwpmcm9tIGltcG9ydGxpYi5hYmMgaW1wb3J0IExvYWRlciwgTWV0YVBhdGhGaW5k
ZXIKZnJvbSBpbXBvcnRsaWIudXRpbCBpbXBvcnQgZmluZF9zcGVjLCBzcGVjX2Zyb21fbG9hZGVy
CgojIEdsb2JhbCB2YXJpYWJsZXMgdG8gaG9sZCBpbXBvcnRlZCBsaWJyYXJpZXMgaWYgbmVlZGVk
Cm5wID0gTm9uZQptcGwgPSBOb25lCnBpbF9pbWcgPSBOb25lCgoKcGx0X3BhdGNoZWQgPSBGYWxz
ZQpwcm9jZXNzZWRfZmlndXJlcyA9IHNldCgpCgoKZGVmIF9wYXJzZV9wb2ludChwb2ludCk6CiAg
ICBpZiBpc2luc3RhbmNlKHBvaW50LCBkYXRldGltZS5kYXRlKToKICAgICAgICByZXR1cm4gcG9p
bnQuaXNvZm9ybWF0KCkKICAgIGlmIGlzaW5zdGFuY2UocG9pbnQsIG5wLmRhdGV0aW1lNjQpOgog
ICAgICAgIHJldHVybiBwb2ludC5hc3R5cGUoImRhdGV0aW1lNjRbc10iKS5hc3R5cGUoc3RyKQog
ICAgcmV0dXJuIHBvaW50CgoKZGVmIF9pc19ncmlkX2xpbmUobGluZTogYW55KSAtPiBib29sOgog
ICAgeF9kYXRhID0gbGluZS5nZXRfeGRhdGEoKQogICAgaWYgbGVuKHhfZGF0YSkgIT0gMjoKICAg
ICAgICByZXR1cm4gRmFsc2UKCiAgICB5X2RhdGEgPSBsaW5lLmdldF95ZGF0YSgpCiAgICBpZiBs
ZW4oeV9kYXRhKSAhPSAyOgogICAgICAgIHJldHVybiBGYWxzZQoKICAgIGlmIHhfZGF0YVswXSA9
PSB4X2RhdGFbMV0gb3IgeV9kYXRhWzBdID09IHlfZGF0YVsxXToKICAgICAgICByZXR1cm4gVHJ1
ZQoKICAgIHJldHVybiBGYWxzZQoKCmRlZiBfZXh0cmFjdF9saW5lX2NoYXJ0X2VsZW1lbnRzKGF4
KToKICAgIGVsZW1lbnRzID0gW10KCiAgICBmb3IgbGluZSBpbiBheC5nZXRfbGluZXMoKToKICAg
ICAgICBpZiBfaXNfZ3JpZF9saW5lKGxpbmUpOgogICAgICAgICAgICBjb250aW51ZQogICAgICAg
IGxhYmVsID0gbGluZS5nZXRfbGFiZWwoKQogICAgICAgIHBvaW50cyA9IFtfcGFyc2VfcG9pbnQo
KHgsIHkpKSBmb3IgeCwgeSBpbiB6aXAobGluZS5nZXRfeGRhdGEoKSwgbGluZS5nZXRfeWRhdGEo
KSldCgogICAgICAgIGVsZW1lbnQgPSB7ImxhYmVsIjogbGFiZWwsICJwb2ludHMiOiBwb2ludHN9
CiAgICAgICAgZWxlbWVudHMuYXBwZW5kKGVsZW1lbnQpCgogICAgcmV0dXJuIGVsZW1lbnRzCgoK
ZGVmIF9leHRyYWN0X3NjYXR0ZXJfY2hhcnRfZWxlbWVudHMoYXgpOgogICAgZWxlbWVudHMgPSBb
XQoKICAgIGZvciBjb2xsZWN0aW9uIGluIGF4LmNvbGxlY3Rpb25zOgogICAgICAgIHBvaW50cyA9
IFtfcGFyc2VfcG9pbnQoKHgsIHkpKSBmb3IgeCwgeSBpbiBjb2xsZWN0aW9uLmdldF9vZmZzZXRz
KCldCiAgICAgICAgZWxlbWVudCA9IHsibGFiZWwiOiBjb2xsZWN0aW9uLmdldF9sYWJlbCgpLCAi
cG9pbnRzIjogcG9pbnRzfQogICAgICAgIGVsZW1lbnRzLmFwcGVuZChlbGVtZW50KQoKICAgIHJl
dHVybiBlbGVtZW50cwoKCmRlZiBfZXh0cmFjdF9iYXJfY2hhcnRfZWxlbWVudHMoYXgpOgogICAg
ZWxlbWVudHMgPSBbXQogICAgY2hhbmdlX29yaWVudGF0aW9uID0gRmFsc2UKCiAgICBmb3IgY29u
dGFpbmVyIGluIGF4LmNvbnRhaW5lcnM6CiAgICAgICAgaGVpZ2h0cyA9IFtyZWN0LmdldF9oZWln
aHQoKSBmb3IgcmVjdCBpbiBjb250YWluZXJdCiAgICAgICAgaWYgYWxsKGhlaWdodCA9PSBoZWln
aHRzWzBdIGZvciBoZWlnaHQgaW4gaGVpZ2h0cyk6CiAgICAgICAgICAgICMgdmVydGljYWwgYmFy
cwogICAgICAgICAgICBjaGFuZ2Vfb3JpZW50YXRpb24gPSBUcnVlCiAgICAgICAgICAgIGxhYmVs
cyA9IFtsYWJlbC5nZXRfdGV4dCgpIGZvciBsYWJlbCBpbiBheC5nZXRfeXRpY2tsYWJlbHMoKV0K
ICAgICAgICAgICAgdmFsdWVzID0gW3JlY3QuZ2V0X3dpZHRoKCkgZm9yIHJlY3QgaW4gY29udGFp
bmVyXQogICAgICAgIGVsc2U6CiAgICAgICAgICAgICMgaG9yaXpvbnRhbCBiYXJzCiAgICAgICAg
ICAgIGxhYmVscyA9IFtsYWJlbC5nZXRfdGV4dCgpIGZvciBsYWJlbCBpbiBheC5nZXRfeHRpY2ts
YWJlbHMoKV0KICAgICAgICAgICAgdmFsdWVzID0gaGVpZ2h0cwogICAgICAgIGZvciBsYWJlbCwg
dmFsdWUgaW4gemlwKGxhYmVscywgdmFsdWVzKToKICAgICAgICAgICAgZWxlbWVudCA9IHsibGFi
ZWwiOiBsYWJlbCwgImdyb3VwIjogY29udGFpbmVyLmdldF9sYWJlbCgpLCAidmFsdWUiOiB2YWx1
ZX0KICAgICAgICAgICAgZWxlbWVudHMuYXBwZW5kKGVsZW1lbnQpCgogICAgcmV0dXJuIGVsZW1l
bnRzLCBjaGFuZ2Vfb3JpZW50YXRpb24KCgpkZWYgX2V4dHJhY3RfcGllX2NoYXJ0X2VsZW1lbnRz
KGF4KToKICAgIGVsZW1lbnRzID0gW10KCiAgICB3ZWRnZXMgPSBbcGF0Y2ggZm9yIHBhdGNoIGlu
IGF4LnBhdGNoZXMgaWYgaXNpbnN0YW5jZShwYXRjaCwgbXBsLnBhdGNoZXMuV2VkZ2UpXQogICAg
aWYgbGVuKHdlZGdlcykgPT0gMDoKICAgICAgICByZXR1cm4gZWxlbWVudHMKCiAgICB0ZXh0cyA9
IFt0ZXh0X29iai5nZXRfdGV4dCgpIGZvciB0ZXh0X29iaiBpbiBheC50ZXh0c10KCiAgICBsYWJl
bHMgPSBbXQogICAgYXV0b3BjdHMgPSBbXQoKICAgIGlmIGxlbih0ZXh0cykgPT0gMiAqIGxlbih3
ZWRnZXMpOgogICAgICAgIGxhYmVscyA9IFt0ZXh0c1tpXSBmb3IgaSBpbiByYW5nZSgwLCAyICog
bGVuKHdlZGdlcyksIDIpXQogICAgICAgIGF1dG9wY3RzID0gW3RleHRzW2ldIGZvciBpIGluIHJh
bmdlKDEsIDIgKiBsZW4od2VkZ2VzKSwgMildCiAgICBlbHNlOgogICAgICAgIGxhYmVscyA9IHRl
eHRzWzogbGVuKHdlZGdlcyldCgogICAgZm9yIGlkeCwgd2VkZ2UgaW4gZW51bWVyYXRlKHdlZGdl
cyk6CiAgICAgICAgZWxlbWVudCA9IHsKICAgICAgICAgICAgImxhYmVsIjogbGFiZWxzW2lkeF0s
CiAgICAgICAgICAgICJhbmdsZSI6IGFicyh3ZWRnZS50aGV0YTIgLSB3ZWRnZS50aGV0YTEpLAog
ICAgICAgICAgICAicmFkaXVzIjogd2VkZ2UuciwKICAgICAgICAgICAgImF1dG9wY3QiOiBhdXRv
cGN0c1tpZHhdIGlmIGF1dG9wY3RzIGFuZCBsZW4oYXV0b3BjdHMpID4gaWR4IGVsc2UgTm9uZSwK
ICAgICAgICB9CiAgICAgICAgZWxlbWVudHMuYXBwZW5kKGVsZW1lbnQpCgogICAgcmV0dXJuIGVs
ZW1lbnRzCgoKIyBweWxpbnQ6IGRpc2FibGU9dG9vLW1hbnktYnJhbmNoZXMKZGVmIF9leHRyYWN0
X2JveF9jaGFydF9lbGVtZW50cyhheCk6CiAgICBjaGFuZ2Vfb3JpZW50YXRpb24gPSBGYWxzZQoK
ICAgIHh0aWNrbGFiZWxzID0gW2xhYmVsLmdldF90ZXh0KCkgZm9yIGxhYmVsIGluIGF4LmdldF94
dGlja2xhYmVscygpXQogICAgYm94ZXMgPSBbXQogICAgZm9yIGxhYmVsLCBib3ggaW4gemlwKHh0
aWNrbGFiZWxzLCBheC5wYXRjaGVzKToKICAgICAgICB2ZXJ0aWNlcyA9IGJveC5nZXRfcGF0aCgp
LnZlcnRpY2VzCiAgICAgICAgeF92ZXJ0aWNlcyA9IGxpc3QodmVydGljZXNbOiwgMF0pCiAgICAg
ICAgeV92ZXJ0aWNlcyA9IGxpc3QodmVydGljZXNbOiwgMV0pCiAgICAgICAgeCA9IG1pbih4X3Zl
cnRpY2VzKQogICAgICAgIHkgPSBtaW4oeV92ZXJ0aWNlcykKCiAgICAgICAgYm94ZXMuYXBwZW5k
KAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAieCI6IHgsCiAgICAgICAgICAgICAgICAi
eSI6IHksCiAgICAgICAgICAgICAgICAibGFiZWwiOiBsYWJlbCwKICAgICAgICAgICAgICAgICJ3
aWR0aCI6IG1heCh4X3ZlcnRpY2VzKSAtIHgsCiAgICAgICAgICAgICAgICAiaGVpZ2h0IjogbWF4
KHlfdmVydGljZXMpIC0geSwKICAgICAgICAgICAgICAgICJvdXRsaWVycyI6IFtdLAogICAgICAg
ICAgICB9CiAgICAgICAgKQoKICAgIG9yaWVudGF0aW9uID0gImhvcml6b250YWwiCiAgICBpZiBh
bGwoYm94WyJoZWlnaHQiXSA9PSBib3hlc1swXVsiaGVpZ2h0Il0gZm9yIGJveCBpbiBib3hlcyk6
CiAgICAgICAgb3JpZW50YXRpb24gPSAidmVydGljYWwiCgogICAgaWYgb3JpZW50YXRpb24gPT0g
InZlcnRpY2FsIjoKICAgICAgICBjaGFuZ2Vfb3JpZW50YXRpb24gPSBUcnVlCiAgICAgICAgZm9y
IGJveCBpbiBib3hlczoKICAgICAgICAgICAgYm94WyJ4Il0sIGJveFsieSJdID0gYm94WyJ5Il0s
IGJveFsieCJdCiAgICAgICAgICAgIGJveFsid2lkdGgiXSwgYm94WyJoZWlnaHQiXSA9IGJveFsi
aGVpZ2h0Il0sIGJveFsid2lkdGgiXQoKICAgIGZvciBsaW5lIGluIGF4LmxpbmVzOgogICAgICAg
IHhkYXRhID0gbGluZS5nZXRfeGRhdGEoKQogICAgICAgIHlkYXRhID0gbGluZS5nZXRfeWRhdGEo
KQoKICAgICAgICBpZiBvcmllbnRhdGlvbiA9PSAidmVydGljYWwiOgogICAgICAgICAgICB4ZGF0
YSwgeWRhdGEgPSB5ZGF0YSwgeGRhdGEKCiAgICAgICAgaWYgbGVuKHhkYXRhKSA8PSAxIG9yIGxl
bih5ZGF0YSkgIT0gMjoKICAgICAgICAgICAgY29udGludWUKCiAgICAgICAgZm9yIGJveCBpbiBi
b3hlczoKICAgICAgICAgICAgaWYgYm94WyJ4Il0gPD0geGRhdGFbMF0gPD0geGRhdGFbMV0gPD0g
Ym94WyJ4Il0gKyBib3hbIndpZHRoIl06CiAgICAgICAgICAgICAgICAjIEhvcml6b250YWwgbGlu
ZSAobWVkaWFuIG9yIGNhcCkKICAgICAgICAgICAgICAgIGlmIGFicyh5ZGF0YVswXSAtIHlkYXRh
WzFdKSA8IDAuMDAxIGFuZCBib3hbInkiXSA8PSB5ZGF0YVswXSA8PSBib3hbInkiXSArIGJveFsi
aGVpZ2h0Il06CiAgICAgICAgICAgICAgICAgICAgYm94WyJtZWRpYW4iXSA9IHlkYXRhWzBdCiAg
ICAgICAgICAgICAgICAjIFZlcnRpY2FsIGxpbmUgKHdoaXNrZXJzKQogICAgICAgICAgICAgICAg
ZWxpZiBhYnMoeGRhdGFbMF0gLSB4ZGF0YVsxXSkgPCAwLjAwMToKICAgICAgICAgICAgICAgICAg
ICB5X21pbiA9IG1pbih5ZGF0YSkKICAgICAgICAgICAgICAgICAgICB5X21heCA9IG1heCh5ZGF0
YSkKCiAgICAgICAgICAgICAgICAgICAgIyBJZiBhdHRhY2hlZCB0byBib3R0b20gb2YgYm94CiAg
ICAgICAgICAgICAgICAgICAgaWYgYWJzKHlfbWF4IC0gYm94WyJ5Il0pIDwgMC4wMDE6CiAgICAg
ICAgICAgICAgICAgICAgICAgIGJveFsid2hpc2tlcl9sb3dlciJdID0geV9taW4KCiAgICAgICAg
ICAgICAgICAgICAgIyBJZiBhdHRhY2hlZCB0byB0b3Agb2YgYm94CiAgICAgICAgICAgICAgICAg
ICAgZWxpZiBhYnMoeV9taW4gLSAoYm94WyJ5Il0gKyBib3hbImhlaWdodCJdKSkgPCAwLjAwMToK
ICAgICAgICAgICAgICAgICAgICAgICAgYm94WyJ3aGlza2VyX3VwcGVyIl0gPSB5X21heAogICAg
ICAgICAgICAgICAgYnJlYWsKCiAgICBvdXRsaWVyX2NhbmRpZGF0ZXMgPSBbXQoKICAgICMgQ2hl
Y2sgZm9yIGFueSBtYXJrZXJzIGluIGFsbCBhcnRpc3RzCiAgICBmb3IgYXJ0aXN0IGluIGF4Lmdl
dF9jaGlsZHJlbigpOgogICAgICAgIGlmIGhhc2F0dHIoYXJ0aXN0LCAiZ2V0X3hkYXRhIikgYW5k
IGhhc2F0dHIoYXJ0aXN0LCAiZ2V0X3lkYXRhIik6CiAgICAgICAgICAgIHRyeToKICAgICAgICAg
ICAgICAgIHhkYXRhID0gYXJ0aXN0LmdldF94ZGF0YSgpCiAgICAgICAgICAgICAgICB5ZGF0YSA9
IGFydGlzdC5nZXRfeWRhdGEoKQoKICAgICAgICAgICAgICAgIGlmIG9yaWVudGF0aW9uID09ICJ2
ZXJ0aWNhbCI6CiAgICAgICAgICAgICAgICAgICAgeGRhdGEsIHlkYXRhID0geWRhdGEsIHhkYXRh
CgogICAgICAgICAgICAgICAgaWYgaXNpbnN0YW5jZSh4ZGF0YSwgKGxpc3QsIG5wLm5kYXJyYXkp
KSBhbmQgaXNpbnN0YW5jZSh5ZGF0YSwgKGxpc3QsIG5wLm5kYXJyYXkpKToKICAgICAgICAgICAg
ICAgICAgICBmb3IgaSBpbiByYW5nZShtaW4obGVuKHhkYXRhKSwgbGVuKHlkYXRhKSkpOgogICAg
ICAgICAgICAgICAgICAgICAgICBvdXRsaWVyX2NhbmRpZGF0ZXMuYXBwZW5kKChmbG9hdCh4ZGF0
YVtpXSksIGZsb2F0KHlkYXRhW2ldKSkpCiAgICAgICAgICAgIGV4Y2VwdDoKICAgICAgICAgICAg
ICAgIHBhc3MKCiAgICAjIEFzc2lnbiBwb2ludHMgdG8gYm94ZXMgYW5kIGRldGVybWluZSBpZiB0
aGV5J3JlIG91dGxpZXJzCiAgICBmb3IgeCwgeSBpbiBvdXRsaWVyX2NhbmRpZGF0ZXM6CiAgICAg
ICAgZm9yIGJveCBpbiBib3hlczoKICAgICAgICAgICAgaWYgYm94WyJ4Il0gPD0geCA8PSBib3hb
IngiXSArIGJveFsid2lkdGgiXToKICAgICAgICAgICAgICAgIGJveF9jZW50ZXIgPSBib3hbIngi
XSArIGJveFsid2lkdGgiXSAvIDIKICAgICAgICAgICAgICAgIGlmIGFicyh4IC0gYm94X2NlbnRl
cikgPCAwLjAwMToKICAgICAgICAgICAgICAgICAgICB5X21pbiA9IGJveFsieSJdCiAgICAgICAg
ICAgICAgICAgICAgeV9tYXggPSBib3hbInkiXSArIGJveFsiaGVpZ2h0Il0KICAgICAgICAgICAg
ICAgICAgICBpZiBib3guZ2V0KCJ3aGlza2VyX2xvd2VyIiwgTm9uZSk6CiAgICAgICAgICAgICAg
ICAgICAgICAgIHlfbWluID0gYm94WyJ3aGlza2VyX2xvd2VyIl0KICAgICAgICAgICAgICAgICAg
ICBpZiBib3guZ2V0KCJ3aGlza2VyX3VwcGVyIiwgTm9uZSk6CiAgICAgICAgICAgICAgICAgICAg
ICAgIHlfbWF4ID0gYm94WyJ3aGlza2VyX3VwcGVyIl0KICAgICAgICAgICAgICAgICAgICBpZiB5
IDwgeV9taW4gb3IgeSA+IHlfbWF4OgogICAgICAgICAgICAgICAgICAgICAgICBib3hbIm91dGxp
ZXJzIl0uYXBwZW5kKHkpCiAgICAgICAgICAgICAgICBicmVhawoKICAgIHJldHVybiBbCiAgICAg
ICAgewogICAgICAgICAgICAibGFiZWwiOiBib3hbImxhYmVsIl0sCiAgICAgICAgICAgICJtaW4i
OiBib3guZ2V0KCJ3aGlza2VyX2xvd2VyIiwgTm9uZSksCiAgICAgICAgICAgICJmaXJzdF9xdWFy
dGlsZSI6IGJveFsieSJdLAogICAgICAgICAgICAibWVkaWFuIjogYm94LmdldCgibWVkaWFuIiwg
Tm9uZSksCiAgICAgICAgICAgICJ0aGlyZF9xdWFydGlsZSI6IGJveFsieSJdICsgYm94WyJoZWln
aHQiXSwKICAgICAgICAgICAgIm1heCI6IGJveC5nZXQoIndoaXNrZXJfdXBwZXIiLCBOb25lKSwK
ICAgICAgICAgICAgIm91dGxpZXJzIjogYm94WyJvdXRsaWVycyJdLAogICAgICAgIH0KICAgICAg
ICBmb3IgYm94IGluIGJveGVzCiAgICBdLCBjaGFuZ2Vfb3JpZW50YXRpb24KCgpkZWYgX3NhdmVf
ZmlndXJlX2FzX2Jhc2U2NChmaWcsIGJib3hfaW5jaGVzPSJ0aWdodCIsIGRwaT0xMDApOgogICAg
IyBGaXJzdCBzYXZlIHdpdGggbWF0cGxvdGxpYgogICAgcG5nX2J1ZmZlciA9IGlvLkJ5dGVzSU8o
KQogICAgZmlnLnNhdmVmaWcocG5nX2J1ZmZlciwgZm9ybWF0PSJwbmciLCBiYm94X2luY2hlcz1i
Ym94X2luY2hlcywgZHBpPWRwaSkKICAgIHBuZ19idWZmZXIuc2VlaygwKQoKICAgICMgT3BlbiB3
aXRoIFBJTCBhbmQgYXBwbHkgbWF4aW11bSBjb21wcmVzc2lvbgogICAgd2l0aCBwaWxfaW1nLm9w
ZW4ocG5nX2J1ZmZlcikgYXMgaW1nOgogICAgICAgIG9wdGltaXplZF9idWZmZXIgPSBpby5CeXRl
c0lPKCkKICAgICAgICBpbWcuc2F2ZShvcHRpbWl6ZWRfYnVmZmVyLCBmb3JtYXQ9InBuZyIsIG9w
dGltaXplPVRydWUsIHF1YWxpdHk9MTAwLCBjb21wcmVzc19sZXZlbD05KQogICAgICAgIG9wdGlt
aXplZF9idWZmZXIuc2VlaygwKQogICAgICAgIHJldHVybiBiYXNlNjQuYjY0ZW5jb2RlKG9wdGlt
aXplZF9idWZmZXIuZ2V0dmFsdWUoKSkuZGVjb2RlKCJ1dGYtOCIpCgoKZGVmIF9nZXRfZmlndXJl
X2hhc2goZmlnKToKICAgIHBuZ19idWZmZXIgPSBpby5CeXRlc0lPKCkKICAgIGZpZy5zYXZlZmln
KHBuZ19idWZmZXIsIGZvcm1hdD0icG5nIiwgZHBpPTUwKQogICAgcmV0dXJuIGhhc2hsaWIubWQ1
KHBuZ19idWZmZXIuZ2V0dmFsdWUoKSkuaGV4ZGlnZXN0KCkKCgpkZWYgX2dldF9jaGFydF90eXBl
KGF4KToKICAgIG9iamVjdHMgPSBsaXN0KAogICAgICAgIGZpbHRlcigKICAgICAgICAgICAgbGFt
YmRhIG9iajogbm90IGlzaW5zdGFuY2Uob2JqLCBtcGwudGV4dC5UZXh0KSBhbmQgbm90IGlzaW5z
dGFuY2Uob2JqLCBtcGwucGF0Y2hlcy5TaGFkb3cpLAogICAgICAgICAgICBheC5fY2hpbGRyZW4s
ICAjIHB5bGludDogZGlzYWJsZT1wcm90ZWN0ZWQtYWNjZXNzCiAgICAgICAgKQogICAgKQoKICAg
ICMgQ2hlY2sgZm9yIExpbmUgcGxvdHMKICAgIGlmIGFsbChpc2luc3RhbmNlKGxpbmUsIG1wbC5s
aW5lcy5MaW5lMkQpIGZvciBsaW5lIGluIG9iamVjdHMpOgogICAgICAgIHJldHVybiAibGluZSIK
CiAgICBpZiBhbGwoaXNpbnN0YW5jZShib3hfb3JfcGF0aCwgKG1wbC5wYXRjaGVzLlBhdGhQYXRj
aCwgbXBsLmxpbmVzLkxpbmUyRCkpIGZvciBib3hfb3JfcGF0aCBpbiBvYmplY3RzKToKICAgICAg
ICByZXR1cm4gImJveF9hbmRfd2hpc2tlciIKCiAgICBmaWx0ZXJlZCA9IFtdCiAgICBmb3Igb2Jq
IGluIG9iamVjdHM6CiAgICAgICAgaWYgaXNpbnN0YW5jZShvYmosIG1wbC5saW5lcy5MaW5lMkQp
IGFuZCBfaXNfZ3JpZF9saW5lKG9iaik6CiAgICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgZmls
dGVyZWQuYXBwZW5kKG9iaikKCiAgICBvYmplY3RzID0gZmlsdGVyZWQKCiAgICAjIENoZWNrIGZv
ciBTY2F0dGVyIHBsb3RzCiAgICBpZiBhbGwoaXNpbnN0YW5jZShwYXRoLCBtcGwuY29sbGVjdGlv
bnMuUGF0aENvbGxlY3Rpb24pIGZvciBwYXRoIGluIG9iamVjdHMpOgogICAgICAgIHJldHVybiAi
c2NhdHRlciIKCiAgICAjIENoZWNrIGZvciBCYXIgcGxvdHMKICAgIGlmIGFsbChpc2luc3RhbmNl
KHJlY3QsIG1wbC5wYXRjaGVzLlJlY3RhbmdsZSkgZm9yIHJlY3QgaW4gb2JqZWN0cyk6CiAgICAg
ICAgcmV0dXJuICJiYXIiCgogICAgIyBDaGVjayBmb3IgUGllIHBsb3RzCiAgICBpZiBhbGwoaXNp
bnN0YW5jZShhcnRpc3QsIG1wbC5wYXRjaGVzLldlZGdlKSBmb3IgYXJ0aXN0IGluIG9iamVjdHMp
OgogICAgICAgIHJldHVybiAicGllIgoKICAgIHJldHVybiAidW5rbm93biIKCgpkZWYgX2lzX2F1
dG9fZW1wdHlfYXhpcyhheCk6CiAgICByZXR1cm4gYXguZ2V0X3N1YnBsb3RzcGVjKCkgaXMgbm90
IE5vbmUgYW5kIG5vdCBheC5oYXNfZGF0YSgpCgoKZGVmIF9pc19jb2xvcmJhcl9heGlzKGF4KToK
ICAgIHJldHVybiBhbnkoCiAgICAgICAgIyBweWxpbnQ6IGRpc2FibGU9cHJvdGVjdGVkLWFjY2Vz
cwogICAgICAgIGlzaW5zdGFuY2UoY2hpbGQsIG1wbC5jb2xvcmJhci5fQ29sb3JiYXJTcGluZSkK
ICAgICAgICBmb3IgY2hpbGQgaW4gYXguZ2V0X2NoaWxkcmVuKCkKICAgICkKCgpkZWYgX2ZpbHRl
cl9vdXRfdW53YW50ZWRfYXhlcyhheGVzKToKICAgIHJldHVybiBbYXggZm9yIGF4IGluIGF4ZXMg
aWYgbm90IF9pc19hdXRvX2VtcHR5X2F4aXMoYXgpIGFuZCBub3QgX2lzX2NvbG9yYmFyX2F4aXMo
YXgpXQoKCmRlZiBfZXh0cmFjdF90aWNrc19kYXRhKGNvbnZlcnRlcjogYW55LCB0aWNrczogYW55
KSAtPiBsaXN0OgogICAgaWYgaXNpbnN0YW5jZShjb252ZXJ0ZXIsIG1wbC5kYXRlcy5fU3dpdGNo
YWJsZURhdGVDb252ZXJ0ZXIpOiAgIyBweWxpbnQ6IGRpc2FibGU9cHJvdGVjdGVkLWFjY2Vzcwog
ICAgICAgIHJldHVybiBbbXBsLmRhdGVzLm51bTJkYXRlKHRpY2spLmlzb2Zvcm1hdCgpIGZvciB0
aWNrIGluIHRpY2tzXQogICAgdHJ5OgogICAgICAgIHJldHVybiBbZmxvYXQodGljaykgZm9yIHRp
Y2sgaW4gdGlja3NdCiAgICBleGNlcHQgRXhjZXB0aW9uOgogICAgICAgIHJldHVybiBsaXN0KHRp
Y2tzKQoKCmRlZiBfZXh0cmFjdF9zY2FsZShjb252ZXJ0ZXIsIHNjYWxlOiBzdHIsIHRpY2tzLCBs
YWJlbHMpIC0+IHN0cjoKICAgIGlmIGlzaW5zdGFuY2UoY29udmVydGVyLCBtcGwuZGF0ZXMuX1N3
aXRjaGFibGVEYXRlQ29udmVydGVyKTogICMgcHlsaW50OiBkaXNhYmxlPXByb3RlY3RlZC1hY2Nl
c3MKICAgICAgICByZXR1cm4gImRhdGV0aW1lIgoKICAgICMgSWYgdGhlIHNjYWxlIGlzIG5vdCBs
aW5lYXIsIGl0IGNhbid0IGJlIGNhdGVnb3JpY2FsCiAgICBpZiBzY2FsZSAhPSAibGluZWFyIjoK
ICAgICAgICByZXR1cm4gc2NhbGUKCiAgICAjIElmIGFsbCB0aGUgdGlja3MgYXJlIGludGVnZXJz
IGFuZCBhcmUgaW4gb3JkZXIgZnJvbSAwIHRvIG4tMQogICAgIyBhbmQgdGhlIGxhYmVscyBhcmVu
J3QgY29ycmVzcG9uZGluZyB0byB0aGUgdGlja3MsIGl0J3MgY2F0ZWdvcmljYWwKICAgIGZvciBp
LCB0aWNrX2FuZF9sYWJlbCBpbiBlbnVtZXJhdGUoemlwKHRpY2tzLCBsYWJlbHMpKToKICAgICAg
ICB0aWNrLCBsYWJlbCA9IHRpY2tfYW5kX2xhYmVsCiAgICAgICAgaWYgaXNpbnN0YW5jZSh0aWNr
LCAoaW50LCBmbG9hdCkpIGFuZCB0aWNrID09IGkgYW5kIHN0cihpKSAhPSBsYWJlbDoKICAgICAg
ICAgICAgY29udGludWUKICAgICAgICAjIEZvdW5kIGEgdGljaywgd2hpY2ggd291bGRuJ3QgYmUg
aW4gYSBjYXRlZ29yaWNhbCBzY2FsZQogICAgICAgIHJldHVybiAibGluZWFyIgoKICAgIHJldHVy
biAiY2F0ZWdvcmljYWwiCgoKZGVmIF9leHRyYWN0X2NoYXJ0X2RhdGEoYXgpOgogICAgZGF0YSA9
IHt9CgogICAgZGF0YVsidGl0bGUiXSA9IGF4LmdldF90aXRsZSgpCgogICAgZGF0YVsieF9sYWJl
bCJdID0gYXguZ2V0X3hsYWJlbCgpCiAgICBkYXRhWyJ5X2xhYmVsIl0gPSBheC5nZXRfeWxhYmVs
KCkKCiAgICB4X3RpY2tfbGFiZWxzID0gW2xhYmVsLmdldF90ZXh0KCkgZm9yIGxhYmVsIGluIGF4
LmdldF94dGlja2xhYmVscygpXQogICAgZGF0YVsieF90aWNrcyJdID0gX2V4dHJhY3RfdGlja3Nf
ZGF0YShheC54YXhpcy5nZXRfY29udmVydGVyKCksIGF4LmdldF94dGlja3MoKSkKICAgIGRhdGFb
InhfdGlja19sYWJlbHMiXSA9IHhfdGlja19sYWJlbHMKICAgIGRhdGFbInhfc2NhbGUiXSA9IF9l
eHRyYWN0X3NjYWxlKGF4LnhheGlzLmdldF9jb252ZXJ0ZXIoKSwgYXguZ2V0X3hzY2FsZSgpLCBh
eC5nZXRfeHRpY2tzKCksIHhfdGlja19sYWJlbHMpCgogICAgeV90aWNrX2xhYmVscyA9IFtsYWJl
bC5nZXRfdGV4dCgpIGZvciBsYWJlbCBpbiBheC5nZXRfeXRpY2tsYWJlbHMoKV0KICAgIGRhdGFb
InlfdGlja3MiXSA9IF9leHRyYWN0X3RpY2tzX2RhdGEoYXgueWF4aXMuZ2V0X2NvbnZlcnRlcigp
LCBheC5nZXRfeXRpY2tzKCkpCiAgICBkYXRhWyJ5X3RpY2tfbGFiZWxzIl0gPSB5X3RpY2tfbGFi
ZWxzCiAgICBkYXRhWyJ5X3NjYWxlIl0gPSBfZXh0cmFjdF9zY2FsZShheC55YXhpcy5nZXRfY29u
dmVydGVyKCksIGF4LmdldF95c2NhbGUoKSwgYXguZ2V0X3l0aWNrcygpLCB5X3RpY2tfbGFiZWxz
KQoKICAgIGNoYXJ0X3R5cGUgPSBfZ2V0X2NoYXJ0X3R5cGUoYXgpCiAgICBlbGVtZW50cyA9IFtd
CiAgICBjaGFuZ2Vfb3JpZW50YXRpb24gPSBGYWxzZQoKICAgIGlmIGNoYXJ0X3R5cGUgPT0gImxp
bmUiOgogICAgICAgIGVsZW1lbnRzID0gX2V4dHJhY3RfbGluZV9jaGFydF9lbGVtZW50cyhheCkK
ICAgIGVsaWYgY2hhcnRfdHlwZSA9PSAic2NhdHRlciI6CiAgICAgICAgZWxlbWVudHMgPSBfZXh0
cmFjdF9zY2F0dGVyX2NoYXJ0X2VsZW1lbnRzKGF4KQogICAgZWxpZiBjaGFydF90eXBlID09ICJi
YXIiOgogICAgICAgIGVsZW1lbnRzLCBjaGFuZ2Vfb3JpZW50YXRpb24gPSBfZXh0cmFjdF9iYXJf
Y2hhcnRfZWxlbWVudHMoYXgpCiAgICBlbGlmIGNoYXJ0X3R5cGUgPT0gImJveF9hbmRfd2hpc2tl
ciI6CiAgICAgICAgZWxlbWVudHMsIGNoYW5nZV9vcmllbnRhdGlvbiA9IF9leHRyYWN0X2JveF9j
aGFydF9lbGVtZW50cyhheCkKICAgIGVsaWYgY2hhcnRfdHlwZSA9PSAicGllIjoKICAgICAgICBl
bGVtZW50cyA9IF9leHRyYWN0X3BpZV9jaGFydF9lbGVtZW50cyhheCkKCiAgICBpZiBjaGFuZ2Vf
b3JpZW50YXRpb246CiAgICAgICAgZGF0YVsieF9sYWJlbCJdLCBkYXRhWyJ5X2xhYmVsIl0gPSBk
YXRhWyJ5X2xhYmVsIl0sIGRhdGFbInhfbGFiZWwiXQoKICAgIGRhdGFbInR5cGUiXSA9IGNoYXJ0
X3R5cGUKICAgIGRhdGFbImVsZW1lbnRzIl0gPSBlbGVtZW50cwoKICAgIHJldHVybiBkYXRhCgoK
ZGVmIF9jdXN0b21fanNvbl9zZXJpYWxpemVyKG9iaik6CiAgICBpZiBpc2luc3RhbmNlKG9iaiwg
bnAuaW50ZWdlcik6CiAgICAgICAgcmV0dXJuIGludChvYmopCiAgICBpZiBpc2luc3RhbmNlKG9i
aiwgbnAuZmxvYXRpbmcpOgogICAgICAgIHJldHVybiBmbG9hdChvYmopCiAgICBpZiBpc2luc3Rh
bmNlKG9iaiwgbnAubmRhcnJheSk6CiAgICAgICAgcmV0dXJuIG9iai50b2xpc3QoKQogICAgaWYg
aXNpbnN0YW5jZShvYmosIHNldCk6CiAgICAgICAgcmV0dXJuIGxpc3Qob2JqKQogICAgcmFpc2Ug
VHlwZUVycm9yKGYiVHlwZSB7dHlwZShvYmopfSBub3Qgc2VyaWFsaXphYmxlIikKCgpkZWYgZXh0
cmFjdF9hbmRfcHJpbnRfZmlndXJlX21ldGFkYXRhKGZpZyk6CiAgICAiIiJFeHRyYWN0IG1ldGFk
YXRhIGZyb20gYSBtYXRwbG90bGliIGZpZ3VyZSBhbmQgcHJpbnQgYXMgSlNPTiIiIgogICAgbWV0
YWRhdGEgPSB7fQogICAgc3VicGxvdHMgPSBbXQoKICAgIGF4ZXMgPSBfZmlsdGVyX291dF91bndh
bnRlZF9heGVzKGZpZy5heGVzKQoKICAgIGZvciBheCBpbiBheGVzOgogICAgICAgIGRhdGEgPSBf
ZXh0cmFjdF9jaGFydF9kYXRhKGF4KQogICAgICAgIHN1YnBsb3RzLmFwcGVuZChkYXRhKQoKICAg
IGlmIGxlbihzdWJwbG90cykgPiAxOgogICAgICAgIG1ldGFkYXRhID0gewogICAgICAgICAgICAi
dGl0bGUiOiBmaWcudGV4dHNbMF0uZ2V0X3RleHQoKSBpZiBmaWcudGV4dHMgYW5kIGxlbihmaWcu
dGV4dHMpID4gMCBlbHNlIE5vbmUsCiAgICAgICAgICAgICJ0eXBlIjogImNvbXBvc2l0ZV9jaGFy
dCIsCiAgICAgICAgICAgICJlbGVtZW50cyI6IHN1YnBsb3RzLAogICAgICAgIH0KICAgIGVsc2U6
CiAgICAgICAgbWV0YWRhdGEgPSBzdWJwbG90c1swXSBpZiBzdWJwbG90cyBhbmQgbGVuKHN1YnBs
b3RzKSA+IDAgZWxzZSB7InR5cGUiOiAidW5rbm93biJ9CgogICAgbWV0YWRhdGFbInBuZyJdID0g
X3NhdmVfZmlndXJlX2FzX2Jhc2U2NChmaWcpCiAgICBqc29uX291dHB1dCA9IHsidHlwZSI6ICJj
aGFydCIsICJ2YWx1ZSI6IG1ldGFkYXRhfQoKICAgIHByaW50KGYiZHRuX2FydGlmYWN0X2szOWZk
Mjp7anNvbi5kdW1wcyhqc29uX291dHB1dCwgZGVmYXVsdD1fY3VzdG9tX2pzb25fc2VyaWFsaXpl
cil9IikKCgpjbGFzcyBNYXRwbG90bGliRmluZGVyKE1ldGFQYXRoRmluZGVyKToKICAgICIiIkN1
c3RvbSBmaW5kZXIgdG8gaW50ZXJjZXB0IG1hdHBsb3RsaWIucHlwbG90IGltcG9ydHMiIiIKCiAg
ICBkZWYgZmluZF9zcGVjKHNlbGYsIGZ1bGxuYW1lLCBwYXRoLCB0YXJnZXQ9Tm9uZSk6ICAjIHB5
bGludDogZGlzYWJsZT11bnVzZWQtYXJndW1lbnQKICAgICAgICBnbG9iYWwgcGx0X3BhdGNoZWQs
IG5wLCBtcGwsIHBpbF9pbWcgICMgcHlsaW50OiBkaXNhYmxlPWdsb2JhbC1zdGF0ZW1lbnQKICAg
ICAgICBpZiBmdWxsbmFtZSA9PSAibWF0cGxvdGxpYi5weXBsb3QiIGFuZCBub3QgcGx0X3BhdGNo
ZWQ6CiAgICAgICAgICAgIHBsdF9wYXRjaGVkID0gVHJ1ZQoKICAgICAgICAgICAgIyBJbXBvcnQg
bnVtcHkgYW5kIG1hdHBsb3RsaWIgb25jZSB3ZSBhcmUgc3VyZSB3ZSBuZWVkIHRoZW0KICAgICAg
ICAgICAgIyBweWxpbnQ6IGRpc2FibGU9aW1wb3J0LW91dHNpZGUtdG9wbGV2ZWwKICAgICAgICAg
ICAgaW1wb3J0IG1hdHBsb3RsaWIKICAgICAgICAgICAgaW1wb3J0IG51bXB5CiAgICAgICAgICAg
IGZyb20gUElMIGltcG9ydCBJbWFnZQoKICAgICAgICAgICAgIyBTdG9yZSB0aGVtIGluIGdsb2Jh
bCB2YXJpYWJsZXMgZm9yIHVzZSB0aHJvdWdob3V0IHRoZSBtb2R1bGUKICAgICAgICAgICAgbnAg
PSBudW1weQogICAgICAgICAgICBtcGwgPSBtYXRwbG90bGliCiAgICAgICAgICAgIHBpbF9pbWcg
PSBJbWFnZQoKICAgICAgICAgICAgb3JpZ2luYWxfc3BlYyA9IGZpbmRfc3BlYyhmdWxsbmFtZSkK
ICAgICAgICAgICAgaWYgb3JpZ2luYWxfc3BlYyBpcyBOb25lOgogICAgICAgICAgICAgICAgcmV0
dXJuIE5vbmUKICAgICAgICAgICAgcmV0dXJuIHNwZWNfZnJvbV9sb2FkZXIoCiAgICAgICAgICAg
ICAgICBmdWxsbmFtZSwKICAgICAgICAgICAgICAgIE1hdHBsb3RsaWJMb2FkZXIob3JpZ2luYWxf
c3BlYy5sb2FkZXIpLAogICAgICAgICAgICAgICAgb3JpZ2luPW9yaWdpbmFsX3NwZWMub3JpZ2lu
LAogICAgICAgICAgICAgICAgaXNfcGFja2FnZT1vcmlnaW5hbF9zcGVjLnN1Ym1vZHVsZV9zZWFy
Y2hfbG9jYXRpb25zIGlzIG5vdCBOb25lLAogICAgICAgICAgICApCiAgICAgICAgcmV0dXJuIE5v
bmUKCgpjbGFzcyBNYXRwbG90bGliTG9hZGVyKExvYWRlcik6CiAgICAiIiJDdXN0b20gbG9hZGVy
IHRvIHBhdGNoIHRoZSBtYXRwbG90bGliLnB5cGxvdCBtb2R1bGUiIiIKCiAgICBkZWYgX19pbml0
X18oc2VsZiwgb3JpZ2luYWxfbG9hZGVyKToKICAgICAgICBzZWxmLm9yaWdpbmFsX2xvYWRlciA9
IG9yaWdpbmFsX2xvYWRlcgoKICAgIGRlZiBjcmVhdGVfbW9kdWxlKHNlbGYsIHNwZWMpOgogICAg
ICAgIHJldHVybiBzZWxmLm9yaWdpbmFsX2xvYWRlci5jcmVhdGVfbW9kdWxlKHNwZWMpCgogICAg
ZGVmIGV4ZWNfbW9kdWxlKHNlbGYsIG1vZHVsZSk6CiAgICAgICAgc2VsZi5vcmlnaW5hbF9sb2Fk
ZXIuZXhlY19tb2R1bGUobW9kdWxlKQogICAgICAgIGlmIGhhc2F0dHIobW9kdWxlLCAic2hvdyIp
OgogICAgICAgICAgICBvcmlnaW5hbF9zaG93ID0gbW9kdWxlLnNob3cKCiAgICAgICAgICAgIGRl
ZiBjdXN0b21fc2hvdygqYXJncywgKiprd2FyZ3MpOgogICAgICAgICAgICAgICAgZ2xvYmFsIHBy
b2Nlc3NlZF9maWd1cmVzICAjIHB5bGludDogZGlzYWJsZT1nbG9iYWwtdmFyaWFibGUtbm90LWFz
c2lnbmVkCiAgICAgICAgICAgICAgICBmaWdfbnVtcyA9IG1vZHVsZS5nZXRfZmlnbnVtcygpCiAg
ICAgICAgICAgICAgICBmb3IgZmlnX251bSBpbiBmaWdfbnVtczoKICAgICAgICAgICAgICAgICAg
ICBmaWcgPSBtb2R1bGUuZmlndXJlKGZpZ19udW0pCiAgICAgICAgICAgICAgICAgICAgZmlnX2hh
c2ggPSBfZ2V0X2ZpZ3VyZV9oYXNoKGZpZykKICAgICAgICAgICAgICAgICAgICBpZiBmaWdfaGFz
aCBub3QgaW4gcHJvY2Vzc2VkX2ZpZ3VyZXM6CiAgICAgICAgICAgICAgICAgICAgICAgIGV4dHJh
Y3RfYW5kX3ByaW50X2ZpZ3VyZV9tZXRhZGF0YShmaWcpCiAgICAgICAgICAgICAgICAgICAgICAg
IHByb2Nlc3NlZF9maWd1cmVzLmFkZChmaWdfaGFzaCkKICAgICAgICAgICAgICAgIHJlc3VsdCA9
IG9yaWdpbmFsX3Nob3coKmFyZ3MsICoqa3dhcmdzKQogICAgICAgICAgICAgICAgbW9kdWxlLmNs
b3NlKCJhbGwiKQogICAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdAoKICAgICAgICAgICAgbW9k
dWxlLnNob3cgPSBjdXN0b21fc2hvdwoKCmRlZiBzZXR1cF91c2VyX2NvZGVfZW52aXJvbm1lbnQo
Y29kZSk6CiAgICAiIiJTZXQgdXAgdGhlIG1vZHVsZSB0byBydW4gdXNlciBjb2RlIGluIiIiCiAg
ICBtb2R1bGUgPSB0eXBlcy5Nb2R1bGVUeXBlKCJfX21haW5fXyIpCiAgICBtb2R1bGUuX19maWxl
X18gPSAiPHRhcmdldF9jb2RlPiIKICAgIHN5cy5tb2R1bGVzWyJfX21haW5fXyJdID0gbW9kdWxl
CiAgICBjb2RlX2xpbmVzID0gY29kZS5zcGxpdGxpbmVzKCkKICAgIGxpbmVjYWNoZS5jYWNoZVsi
PHRhcmdldF9jb2RlPiJdID0gKGxlbihjb2RlKSwgTm9uZSwgY29kZV9saW5lcywgIjx0YXJnZXRf
Y29kZT4iKQogICAgcmV0dXJuIG1vZHVsZQoKCmRlZiBydW5fdXNlcl9jb2RlKGNvZGUpOgogICAg
IiIiUnVuIHRoZSB1c2VyIGNvZGUgd2l0aCB0aGUgbWF0cGxvdGxpYiBpbnRlcmNlcHRvciBpbnN0
YWxsZWQiIiIKICAgICMgSW5zdGFsbCBtYXRwbG90bGliIGludGVyY2VwdG9yCiAgICBzeXMubWV0
YV9wYXRoLmluc2VydCgwLCBNYXRwbG90bGliRmluZGVyKCkpCgogICAgIyBTZXQgdXAgY2xlYW4g
ZW52aXJvbm1lbnQgZm9yIHVzZXIgY29kZQogICAgbW9kdWxlID0gc2V0dXBfdXNlcl9jb2RlX2Vu
dmlyb25tZW50KGNvZGUpCgogICAgIyBDb21waWxlIGFuZCBydW4gdGhlIGNvZGUKICAgIGNvbXBp
bGVkID0gY29tcGlsZShjb2RlLCAiPHRhcmdldF9jb2RlPiIsICJleGVjIikKCiAgICAjIEV4ZWN1
dGUgaW4gdGhlIG1vZHVsZSdzIG5hbWVzcGFjZQogICAgZXhlYyhjb21waWxlZCwgbW9kdWxlLl9f
ZGljdF9fKSAgIyBweWxpbnQ6IGRpc2FibGU9ZXhlYy11c2VkCgoKaWYgX19uYW1lX18gPT0gIl9f
bWFpbl9fIjoKICAgIHRyeToKICAgICAgICAjIEdldCB0aGUgZW5jb2RlZCB1c2VyIGNvZGUKICAg
ICAgICB1c2VyX2NvZGUgPSBiYXNlNjQuYjY0ZGVjb2RlKCJ7ZW5jb2RlZF9jb2RlfSIpLmRlY29k
ZSgpCgogICAgICAgICMgUnVuIHRoZSBjb2RlCiAgICAgICAgcnVuX3VzZXJfY29kZSh1c2VyX2Nv
ZGUpCiAgICBleGNlcHQgRXhjZXB0aW9uOgogICAgICAgICMgUHJpbnQgb25seSB0aGUgcmVsZXZh
bnQgcGFydHMgb2YgdGhlIHRyYWNlYmFjawogICAgICAgIGV4Y190eXBlLCBleGNfdmFsdWUsIGV4
Y190YiA9IHN5cy5leGNfaW5mbygpCgogICAgICAgICMgRmlsdGVyIHRyYWNlYmFjayB0byBvbmx5
IHNob3cgdXNlciBjb2RlIGZyYW1lcwogICAgICAgIGZpbHRlcmVkX3RiID0gW10KICAgICAgICB0
YiA9IGV4Y190YgogICAgICAgIHdoaWxlIHRiIGlzIG5vdCBOb25lOgogICAgICAgICAgICBpZiB0
Yi50Yl9mcmFtZS5mX2NvZGUuY29fZmlsZW5hbWUgPT0gIjx0YXJnZXRfY29kZT4iOgogICAgICAg
ICAgICAgICAgZmlsdGVyZWRfdGIuYXBwZW5kKHRiKQogICAgICAgICAgICB0YiA9IHRiLnRiX25l
eHQKCiAgICAgICAgaWYgZmlsdGVyZWRfdGI6CiAgICAgICAgICAgICMgQ3JlYXRlIGEgbmV3IHRy
YWNlYmFjayBmcm9tIHRoZSBmaWx0ZXJlZCBmcmFtZXMKICAgICAgICAgICAgZXhjX3ZhbHVlLl9f
dHJhY2ViYWNrX18gPSBmaWx0ZXJlZF90YlstMV0KICAgICAgICAgICAgdHJhY2ViYWNrLnByaW50
X2V4Y2VwdGlvbihleGNfdHlwZSwgZXhjX3ZhbHVlLCBleGNfdmFsdWUuX190cmFjZWJhY2tfXykK
ICAgICAgICBlbHNlOgogICAgICAgICAgICAjIEZhbGxiYWNrIGlmIG5vIHVzZXIgY29kZSBmcmFt
ZXMgZm91bmQgLSByYWlzZSB0aGUgb3JpZ2luYWwgZXhjZXB0aW9uIHR5cGUKICAgICAgICAgICAg
IyB3aXRoIHRoZSBvcmlnaW5hbCBtZXNzYWdlIGJ1dCBjcmVhdGUgYSBmcmVzaCB0cmFjZWJhY2sK
ICAgICAgICAgICAgcmFpc2UgZXhjX3R5cGUoc3RyKGV4Y192YWx1ZSkpIGZyb20gTm9uZQoKICAg
ICAgICBzeXMuZXhpdCgxKQo=
"""
