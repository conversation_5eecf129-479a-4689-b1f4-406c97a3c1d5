#!/usr/bin/env python3
"""
OpenHands Startup Script with Port Checking and Better Error Handling
"""

import os
import sys
import socket
import time
import subprocess
from pathlib import Path

def find_available_port(start_port=3000, end_port=3100):
    """Find an available port in the given range"""
    print(f"🔍 Checking for available ports in range {start_port}-{end_port}...")
    
    for port in range(start_port, end_port + 1):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                print(f"✅ Port {port} is available")
                return port
        except OSError:
            print(f"❌ Port {port} is in use")
            continue
    
    print(f"❌ No available ports found in range {start_port}-{end_port}")
    return None

def load_environment():
    """Load environment variables from .env file"""
    env_file = Path("openhands/configs/.env")
    if not env_file.exists():
        print(f"❌ Environment file not found: {env_file}")
        return False
    
    print(f"📋 Loading environment from: {env_file}")
    env_vars = {}
    
    with open(env_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                key, value = key.strip(), value.strip()
                os.environ[key] = value
                env_vars[key] = value
                
                # Don't print sensitive information
                if 'API_KEY' not in key and 'PASSWORD' not in key:
                    print(f"   {key}={value}")
    
    return True

def test_ollama_connection():
    """Test connection to Ollama server"""
    import requests
    
    ollama_url = os.environ.get('LLM_BASE_URL', 'http://************:11434')
    print(f"🔍 Testing Ollama connection to: {ollama_url}")
    
    try:
        response = requests.get(f"{ollama_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama server accessible with {len(models)} models")
            
            # Check configured model
            configured_model = os.environ.get('LLM_MODEL', '').replace('ollama/', '')
            if configured_model:
                model_exists = any(m['name'] == configured_model for m in models)
                if model_exists:
                    print(f"✅ Model '{configured_model}' is available")
                else:
                    print(f"⚠️  Model '{configured_model}' not found")
                    print("Available models:")
                    for model in models[:3]:
                        print(f"   - {model['name']}")
            return True
        else:
            print(f"❌ Ollama server returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to Ollama: {e}")
        return False

def start_openhands_server(port):
    """Start OpenHands server using different methods"""
    print(f"🚀 Starting OpenHands server on port {port}...")
    
    # Set the port in environment
    os.environ['PORT'] = str(port)
    os.environ['HOST'] = '0.0.0.0'
    
    # Method 1: Try uvicorn with FastAPI app
    try:
        print("📝 Attempting Method 1: uvicorn with FastAPI app...")
        import uvicorn
        from openhands.server.app import app
        
        print(f"🌐 Server starting at: http://localhost:{port}")
        print("🛑 Press Ctrl+C to stop the server")
        print("=" * 50)
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=port,
            log_level="info",
            access_log=True
        )
        return True
        
    except ImportError as e:
        print(f"⚠️  Method 1 failed (FastAPI app import): {e}")
    except Exception as e:
        print(f"⚠️  Method 1 failed: {e}")
    
    # Method 2: Try the listen module
    try:
        print("📝 Attempting Method 2: listen module...")
        from openhands.server.listen import main
        main()
        return True
        
    except ImportError as e:
        print(f"⚠️  Method 2 failed (listen import): {e}")
    except Exception as e:
        print(f"⚠️  Method 2 failed: {e}")
    
    # Method 3: Try running as module
    try:
        print("📝 Attempting Method 3: running as module...")
        result = subprocess.run([
            sys.executable, "-m", "openhands.server",
            "--host", "0.0.0.0",
            "--port", str(port)
        ], check=True)
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"⚠️  Method 3 failed: {e}")
    except Exception as e:
        print(f"⚠️  Method 3 failed: {e}")
    
    # Method 4: Try direct server start
    try:
        print("📝 Attempting Method 4: direct server...")
        import openhands.server
        # Try to find and run the server
        server_path = Path(openhands.server.__file__).parent / "__main__.py"
        if server_path.exists():
            result = subprocess.run([
                sys.executable, str(server_path),
                "--port", str(port)
            ], check=True)
            return True
        else:
            print("⚠️  Method 4: __main__.py not found")
            
    except Exception as e:
        print(f"⚠️  Method 4 failed: {e}")
    
    print("❌ All startup methods failed")
    return False

def main():
    """Main function"""
    print("🦙 OpenHands Startup with Port Checking")
    print("=" * 40)
    
    # Load environment
    if not load_environment():
        return 1
    
    # Find available port
    port = find_available_port()
    if not port:
        print("❌ No available ports found")
        return 1
    
    # Test Ollama connection
    print("\n" + "=" * 40)
    if not test_ollama_connection():
        print("⚠️  Ollama connection failed, but continuing...")
    
    # Start server
    print("\n" + "=" * 40)
    try:
        if start_openhands_server(port):
            print("✅ Server started successfully")
            return 0
        else:
            print("❌ Failed to start server")
            return 1
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        return 0
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
