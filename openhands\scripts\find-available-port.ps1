# Find Available Port Script
# This script finds an available port for OpenHands

param(
    [int]$StartPort = 3000,
    [int]$EndPort = 3100,
    [switch]$ShowUsedPorts
)

Write-Host "🔍 Checking for available ports..." -ForegroundColor Cyan

function Test-PortAvailable {
    param([int]$Port)
    
    try {
        $listener = [System.Net.Sockets.TcpListener]::new([System.Net.IPAddress]::Any, $Port)
        $listener.Start()
        $listener.Stop()
        return $true
    } catch {
        return $false
    }
}

function Get-UsedPorts {
    try {
        $connections = netstat -ano | Select-String ":(\d+)\s" | ForEach-Object {
            if ($_.Line -match ":(\d+)\s") {
                [int]$matches[1]
            }
        } | Sort-Object -Unique
        return $connections
    } catch {
        Write-Warning "Could not get used ports list"
        return @()
    }
}

# Show currently used ports if requested
if ($ShowUsedPorts) {
    Write-Host "`n📊 Currently used ports:" -ForegroundColor Yellow
    $usedPorts = Get-UsedPorts
    $relevantPorts = $usedPorts | Where-Object { $_ -ge $StartPort -and $_ -le $EndPort }
    if ($relevantPorts) {
        foreach ($port in $relevantPorts) {
            Write-Host "  ❌ Port $port is in use" -ForegroundColor Red
        }
    } else {
        Write-Host "  ✅ No ports in range $StartPort-$EndPort are in use" -ForegroundColor Green
    }
}

# Find available ports
Write-Host "`n🔍 Scanning for available ports in range $StartPort-$EndPort..." -ForegroundColor Yellow

$availablePorts = @()
$checkedPorts = 0

for ($port = $StartPort; $port -le $EndPort; $port++) {
    $checkedPorts++
    if (Test-PortAvailable -Port $port) {
        $availablePorts += $port
        Write-Host "  ✅ Port $port is available" -ForegroundColor Green
        
        # Stop after finding first 5 available ports
        if ($availablePorts.Count -ge 5) {
            break
        }
    } else {
        Write-Host "  ❌ Port $port is in use" -ForegroundColor Red
    }
}

Write-Host "`n📋 Summary:" -ForegroundColor Cyan
Write-Host "  Ports checked: $checkedPorts" -ForegroundColor White
Write-Host "  Available ports found: $($availablePorts.Count)" -ForegroundColor White

if ($availablePorts.Count -gt 0) {
    Write-Host "`n🎯 Recommended ports:" -ForegroundColor Green
    foreach ($port in $availablePorts) {
        Write-Host "  ✅ $port" -ForegroundColor Green
    }
    
    $recommendedPort = $availablePorts[0]
    Write-Host "`n💡 Recommended port for OpenHands: $recommendedPort" -ForegroundColor Yellow
    
    # Update the environment file with the recommended port
    $envFile = "openhands\configs\.env"
    if (Test-Path $envFile) {
        Write-Host "`n🔧 Updating configuration file..." -ForegroundColor Yellow
        
        $content = Get-Content $envFile
        $updated = $false
        
        # Update existing PORT line or add new one
        for ($i = 0; $i -lt $content.Length; $i++) {
            if ($content[$i] -match "^PORT=") {
                $content[$i] = "PORT=$recommendedPort"
                $updated = $true
                break
            }
        }
        
        if (-not $updated) {
            # Add PORT setting to the file
            $content += ""
            $content += "# Server Configuration"
            $content += "PORT=$recommendedPort"
            $content += "HOST=0.0.0.0"
        }
        
        $content | Set-Content $envFile
        Write-Host "  ✅ Updated $envFile with PORT=$recommendedPort" -ForegroundColor Green
    }
    
    # Return the recommended port for use in other scripts
    return $recommendedPort
    
} else {
    Write-Host "`n❌ No available ports found in range $StartPort-$EndPort" -ForegroundColor Red
    Write-Host "💡 Try expanding the range or check what's using the ports" -ForegroundColor Yellow
    return $null
}
