#!/usr/bin/env python3
"""
Debug test for OpenHands startup issues
"""

print("🔍 OpenHands Debug Test")
print("=" * 25)

try:
    print("1. Testing basic imports...")
    import sys
    print(f"   Python: {sys.version}")
    
    import openhands
    print(f"   OpenHands: {openhands.__version__}")
    
    print("2. Testing server imports...")
    import openhands.server
    print("   ✅ Server module imported")
    
    import openhands.server.app
    print("   ✅ App module imported")
    
    from openhands.server.app import app
    print("   ✅ FastAPI app imported")
    
    print("3. Testing uvicorn...")
    import uvicorn
    print("   ✅ Uvicorn imported")
    
    print("4. Testing environment...")
    import os
    os.environ['LLM_MODEL'] = 'ollama/qwen2.5-coder:7b'
    os.environ['LLM_BASE_URL'] = 'http://************:11434'
    os.environ['LLM_API_KEY'] = 'ollama'
    print("   ✅ Environment variables set")
    
    print("5. Testing port availability...")
    import socket
    port = 3003
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        result = s.connect_ex(('localhost', port))
        if result != 0:
            print(f"   ✅ Port {port} is available")
        else:
            print(f"   ❌ Port {port} is in use")
            port = 3004
    
    print(f"6. Starting server on port {port}...")
    print(f"   🌐 URL: http://localhost:{port}")
    print("   🛑 Press Ctrl+C to stop")
    print("=" * 40)
    
    # Start the server
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=port,
        log_level="info",
        access_log=True
    )
    
except KeyboardInterrupt:
    print("\n🛑 Stopped by user")
except Exception as e:
    print(f"\n❌ Error: {e}")
    import traceback
    traceback.print_exc()
