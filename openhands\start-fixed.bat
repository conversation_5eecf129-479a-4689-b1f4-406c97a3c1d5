@echo off
echo 🔧 OpenHands Startup (Fixed Python Environment)
echo ===============================================

REM Stop any existing container
echo 🛑 Stopping existing OpenHands container...
docker stop openhands-app 2>nul
docker rm openhands-app 2>nul

REM Set environment variables for Ollama
set LLM_MODEL=ollama/qwen2.5-coder:7b
set LLM_BASE_URL=http://************:11434
set LLM_API_KEY=ollama

echo 🔧 Configuration:
echo   LLM_MODEL=%LLM_MODEL%
echo   LLM_BASE_URL=%LLM_BASE_URL%
echo   Port: 3000

echo.
echo 🧪 Testing Ollama connection...
curl -s %LLM_BASE_URL%/api/tags >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Ollama server is accessible
) else (
    echo ⚠️  Cannot connect to Ollama server
    echo    Continuing anyway...
)

echo.
echo 🚀 Starting OpenHands with fixed environment...
echo 🌐 Access at: http://localhost:3000
echo 🛑 Press Ctrl+C to stop
echo.

REM Create OpenHands data directory
if not exist "%USERPROFILE%\.openhands" mkdir "%USERPROFILE%\.openhands"

REM Start with additional environment variables to fix Python issues
docker run -it --rm ^
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.45-nikolaik ^
    -e LOG_ALL_EVENTS=true ^
    -e LLM_MODEL=%LLM_MODEL% ^
    -e LLM_BASE_URL=%LLM_BASE_URL% ^
    -e LLM_API_KEY=%LLM_API_KEY% ^
    -e PYTHONPATH=/app ^
    -e PYTHONDONTWRITEBYTECODE=1 ^
    -e PYTHONUNBUFFERED=1 ^
    -e PYTHONIOENCODING=utf-8 ^
    -v /var/run/docker.sock:/var/run/docker.sock ^
    -v "%USERPROFILE%\.openhands":/home/<USER>/.openhands ^
    -p 3000:3000 ^
    --add-host host.docker.internal:host-gateway ^
    --name openhands-app ^
    docker.all-hands.dev/all-hands-ai/openhands:0.45

echo.
echo 🏁 OpenHands stopped
pause
