# OpenHands Configuration Template
# Copy this file to .env and fill in your values

# =============================================================================
# CORE CONFIGURATION
# =============================================================================

# OpenHands workspace directory
WORKSPACE_DIR=./workspaces

# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Log directory
LOG_DIR=./logs

# =============================================================================
# LLM CONFIGURATION
# =============================================================================

# Primary LLM Provider (openai, anthropic, azure, ollama, etc.)
LLM_MODEL=gpt-4

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# Anthropic Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_azure_api_key_here
AZURE_OPENAI_ENDPOINT=your_azure_endpoint_here
AZURE_OPENAI_API_VERSION=2023-12-01-preview

# Local Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Docker settings
DOCKER_HOST=unix:///var/run/docker.sock
SANDBOX_CONTAINER_IMAGE=ghcr.io/all-hands-ai/runtime:0.9-nikolaik

# Container resource limits
SANDBOX_MEMORY_LIMIT=4g
SANDBOX_CPU_LIMIT=2

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT Secret for authentication
JWT_SECRET=your_jwt_secret_here

# Allowed hosts (comma-separated)
ALLOWED_HOSTS=localhost,127.0.0.1

# Enable/disable sandbox isolation
SANDBOX_ENABLED=true

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Maximum number of concurrent tasks
MAX_CONCURRENT_TASKS=3

# Request timeout (seconds)
REQUEST_TIMEOUT=300

# Memory limit per workspace (MB)
WORKSPACE_MEMORY_LIMIT=1024

# =============================================================================
# MONITORING & TELEMETRY
# =============================================================================

# Enable telemetry collection
TELEMETRY_ENABLED=false

# Monitoring endpoint
MONITORING_ENDPOINT=http://localhost:8080/metrics

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Debug mode
DEBUG=false

# Development server port
PORT=3000

# Hot reload for development
HOT_RELOAD=false

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Backup directory
BACKUP_DIR=./backups

# Backup retention days
BACKUP_RETENTION_DAYS=30

# Auto-backup interval (hours)
AUTO_BACKUP_INTERVAL=24
