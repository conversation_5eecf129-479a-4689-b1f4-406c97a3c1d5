# Docker Compose configuration for OpenHands CLI
# This provides a containerized environment for OpenHands

version: '3.8'

services:
  openhands:
    image: ghcr.io/all-hands-ai/openhands:latest
    container_name: openhands-cli
    restart: unless-stopped
    
    # Environment configuration
    environment:
      - WORKSPACE_DIR=/workspace
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LLM_MODEL=${LLM_MODEL:-gpt-4}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - SANDBOX_ENABLED=${SANDBOX_ENABLED:-true}
      - DEBUG=${DEBUG:-false}
    
    # Volume mounts
    volumes:
      - ./workspaces:/workspace
      - ./logs:/logs
      - ./configs:/configs
      - /var/run/docker.sock:/var/run/docker.sock
    
    # Port mapping
    ports:
      - "3000:3000"
      - "8080:8080"  # Metrics port
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Security settings
    security_opt:
      - no-new-privileges:true
    
    # Network configuration
    networks:
      - openhands-network

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: openhands-redis
    restart: unless-stopped
    
    # Redis configuration
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    
    volumes:
      - redis-data:/data
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    
    # Health check
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
    
    networks:
      - openhands-network

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: openhands-prometheus
    restart: unless-stopped
    
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    
    volumes:
      - ./configs/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    
    ports:
      - "9090:9090"
    
    networks:
      - openhands-network

  # Log aggregation with Grafana (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: openhands-grafana
    restart: unless-stopped
    
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    
    volumes:
      - grafana-data:/var/lib/grafana
      - ./configs/grafana:/etc/grafana/provisioning
    
    ports:
      - "3001:3000"
    
    networks:
      - openhands-network

# Network configuration
networks:
  openhands-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volume definitions
volumes:
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
