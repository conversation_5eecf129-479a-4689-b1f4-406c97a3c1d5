Metadata-Version: 2.4
Name: daytona
Version: 0.21.1
Summary: Python SDK for Daytona
Author-email: "Daytona Platforms Inc." <<EMAIL>>
License: Apache-2.0
Project-URL: Documentation, https://www.daytona.io/docs
Project-URL: Repository, https://github.com/daytonaio/daytona
Project-URL: Issues, https://github.com/daytonaio/daytona/issues
Keywords: daytona,sdk
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: daytona_api_client<0.22.0,>=0.21.0
Requires-Dist: daytona_api_client_async<0.22.0,>=0.21.0
Requires-Dist: environs<10.0.0,>=9.5.0
Requires-Dist: marshmallow<4.0.0,>=3.19.0
Requires-Dist: pydantic<3.0.0,>=2.4.2
Requires-Dist: python-dateutil<3.0.0,>=2.8.2
Requires-Dist: urllib3<3.0.0,>=2.0.7
Requires-Dist: Deprecated<2.0.0,>=1.2.18
Requires-Dist: httpx<0.29.0,>=0.28.0
Requires-Dist: aiofiles<24.2.0,>=24.1.0
Requires-Dist: aiohttp<4.0.0,>=3.12.0
Requires-Dist: aiohttp_retry<3.0.0,>=2.9.0
Requires-Dist: toml<0.11.0,>=0.10.0
Requires-Dist: aioboto3<15.0.0,>=14.0.0
Requires-Dist: boto3<2.0.0,>=1.0.0
Requires-Dist: importlib-metadata<6.0.0,>=1.0.0; python_version < "3.8"
Provides-Extra: dev
Requires-Dist: pydoc-markdown>=4.8.2; extra == "dev"
Requires-Dist: build>=1.0.3; extra == "dev"
Requires-Dist: wheel>=0.41.2; extra == "dev"
Requires-Dist: setuptools>=68.0.0; extra == "dev"
Requires-Dist: twine>=4.0.2; extra == "dev"
Requires-Dist: nbqa<2.0.0,>=1.9.1; extra == "dev"
Requires-Dist: black[jupyter]<24.0.0,>=23.1.0; extra == "dev"
Requires-Dist: pylint<4.0.0,>=3.3.4; extra == "dev"
Requires-Dist: isort<6.0.0,>=5.10.0; extra == "dev"
Requires-Dist: matplotlib<3.11.0,>=3.10.0; extra == "dev"
Requires-Dist: unasync<0.7.0,>=0.6.0; extra == "dev"
Dynamic: license-file

# Daytona SDK for Python

A Python SDK for interacting with the Daytona API, providing a simple interface for Daytona Sandbox management, Git operations, file system operations, and language server protocol support.

## Installation

You can install the package using pip:

```bash
pip install daytona
```

## Quick Start

Here's a simple example of using the SDK:

```python
from daytona import Daytona

# Initialize using environment variables
daytona = Daytona()

# Create a sandbox
sandbox = daytona.create()

# Run code in the sandbox
response = sandbox.process.code_run('print("Hello World!")')
print(response.result)

# Clean up when done
daytona.delete(sandbox)
```

## Configuration

The SDK can be configured using environment variables or by passing a configuration object:

```python
from daytona import Daytona, DaytonaConfig

# Initialize with configuration
config = DaytonaConfig(
    api_key="your-api-key",
    api_url="your-api-url",
    target="us"
)
daytona = Daytona(config)
```

Or using environment variables:

- `DAYTONA_API_KEY`: Your Daytona API key
- `DAYTONA_API_URL`: The Daytona API URL
- `DAYTONA_TARGET`: Your target environment

You can also customize sandbox creation:

```python
sandbox = daytona.create(CreateSandboxFromSnapshotParams(
    language="python",
    env_vars={"PYTHON_ENV": "development"},
    auto_stop_interval=60,  # Auto-stop after 1 hour of inactivity
    auto_archive_interval=60  # Auto-archive after a Sandbox has been stopped for 1 hour
))
```

## Features

- **Sandbox Management**: Create, manage and remove sandboxes
- **Git Operations**: Clone repositories, manage branches, and more
- **File System Operations**: Upload, download, search and manipulate files
- **Language Server Protocol**: Interact with language servers for code intelligence
- **Process Management**: Execute code and commands in sandboxes

## Examples

### Execute Commands

```python
# Execute a shell command
response = sandbox.process.exec('echo "Hello, World!"')
print(response.result)

# Run Python code
response = sandbox.process.code_run('''
x = 10
y = 20
print(f"Sum: {x + y}")
''')
print(response.result)
```

### File Operations

```python
# Upload a file
sandbox.fs.upload_file(b'Hello, World!', 'path/to/file.txt')

# Download a file
content = sandbox.fs.download_file('path/to/file.txt')

# Search for files
matches = sandbox.fs.find_files(root_dir, 'search_pattern')
```

### Git Operations

```python
# Clone a repository
sandbox.git.clone('https://github.com/example/repo', 'path/to/clone')

# List branches
branches = sandbox.git.branches('path/to/repo')

# Add files
sandbox.git.add('path/to/repo', ['file1.txt', 'file2.txt'])
```

### Language Server Protocol

```python
# Create and start a language server
lsp = sandbox.create_lsp_server('typescript', 'path/to/project')
lsp.start()

# Notify the lsp for the file
lsp.did_open('path/to/file.ts')

# Get document symbols
symbols = lsp.document_symbols('path/to/file.ts')

# Get completions
completions = lsp.completions('path/to/file.ts', {"line": 10, "character": 15})
```

## Contributing

Daytona is Open Source under the [Apache License 2.0](/libs/sdk-python/LICENSE), and is the [copyright of its contributors](/NOTICE). If you would like to contribute to the software, read the Developer Certificate of Origin Version 1.1 (https://developercertificate.org/). Afterwards, navigate to the [contributing guide](/CONTRIBUTING.md) to get started.

Code in [\_sync](/libs/sdk-python/src/daytona/_sync/) directory shouldn't be edited directly. It should be generated from the corresponding async code in the [\_async](/libs/sdk-python/src/daytona/_async/) directory using the [sync_generator.py](/libs/sdk-python/scripts/sync_generator.py) script.
