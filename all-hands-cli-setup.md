# All-Hands CLI Mode Setup Guide

## Overview
All-Hands (formerly OpenHands) CLI mode provides a powerful terminal-based interface for AI-powered software engineering tasks. This guide covers setup, configuration, and enhancements for your Windows/WSL environment.

## Prerequisites

### System Requirements
- Python 3.12 or higher (Python 3.14 not currently supported)
- Docker (for containerized setup)
- WSL2 (for Windows users)
- Git

### Environment Variables Setup
Create a `.env` file in your workspace:

```bash
# Core Configuration
LLM_MODEL="anthropic/claude-sonnet-4-20250514"
LLM_API_KEY="your-api-key-here"
SANDBOX_VOLUMES="z:/vscode:/workspace"

# Optional Advanced Settings
SANDBOX_RUNTIME_CONTAINER_IMAGE="docker.all-hands.dev/all-hands-ai/runtime:0.45-nikolaik"
SANDBOX_USER_ID="1000"
```

## Installation Methods

### Method 1: Python Installation (Recommended)

```bash
# Install via pip
pip install openhands-ai

# Or use uvx for isolated environment
uvx --python 3.12 --from openhands-ai openhands
```

### Method 2: Docker Installation

```bash
# Set environment variables
export SANDBOX_VOLUMES="z:/vscode:/workspace"
export LLM_MODEL="anthropic/claude-sonnet-4-20250514"
export LLM_API_KEY="your-api-key-here"

# Run Docker container
docker run -it \
    --pull=always \
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.45-nikolaik \
    -e SANDBOX_USER_ID=$(id -u) \
    -e SANDBOX_VOLUMES=$SANDBOX_VOLUMES \
    -e LLM_API_KEY=$LLM_API_KEY \
    -e LLM_MODEL=$LLM_MODEL \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -v ~/.openhands:/.openhands \
    --add-host host.docker.internal:host-gateway \
    --name openhands-app-$(date +%Y%m%d%H%M%S) \
    docker.all-hands.dev/all-hands-ai/openhands:0.45 \
    python -m openhands.cli.main --override-cli-mode true
```

### Method 3: Development Setup

```bash
# Clone repository
git clone https://github.com/All-Hands-AI/OpenHands.git
cd OpenHands

# Install with Poetry
poetry install
poetry run python -m openhands.cli.main
```

## Configuration

### Basic Configuration
1. Launch CLI: `openhands`
2. Use `/settings` command to configure:
   - Model selection
   - API keys
   - Advanced options

### Advanced Configuration
Create `~/.openhands/config.toml`:

```toml
[core]
workspace_base = "z:/vscode"
persist_sandbox = true

[llm]
model = "anthropic/claude-sonnet-4-20250514"
api_key = "your-api-key"
temperature = 0.1
max_tokens = 4096

[agent]
memory_enabled = true
confirmation_mode = false

[sandbox]
runtime_container_image = "docker.all-hands.dev/all-hands-ai/runtime:0.45-nikolaik"
user_id = 1000
```

## CLI Commands Reference

| Command | Description |
|---------|-------------|
| `/help` | Show all available commands |
| `/exit` | Exit the application |
| `/init` | Initialize repository for agent exploration |
| `/status` | Show conversation details and metrics |
| `/new` | Start new conversation |
| `/settings` | View/modify LLM/agent settings |
| `/resume` | Resume paused agent |
| `Ctrl-P` | Pause running agent |

## Windows/WSL Integration

### WSL Configuration
Create `/etc/wsl.conf` in WSL:

```ini
[automount]
enabled = true
root = /mnt/
options = "metadata,umask=22,fmask=11"

[network]
generateHosts = true
generateResolvConf = true

[filesystem]
umask = 022
```

### Path Translation Fix
Use the existing `fix-wsl-path-translation.ps1` script to resolve path issues.

## Usage Examples

### Basic Task Execution
```
> Create a Python script that analyzes CSV data
> Implement unit tests for the data analysis functions
> Set up a CI/CD pipeline for this project
```

### Repository Initialization
```
> /init
> Analyze this codebase and suggest improvements
> Create documentation for the main modules
```

## Troubleshooting

### Common Issues
1. **Permission errors**: Ensure `SANDBOX_USER_ID` matches your user ID
2. **Path issues**: Use the WSL path translation fix script
3. **Docker connectivity**: Verify Docker daemon is running
4. **API key errors**: Check environment variables and config

### Debug Commands
```bash
# Check OpenHands installation
openhands --version

# Verify Docker setup
docker ps
docker images | grep openhands

# Test WSL integration
wsl --version
wsl mount
```

## Performance Optimization

### Memory Management
- Use `memory_enabled = true` for better context retention
- Monitor container memory usage
- Clean up old conversations regularly

### Network Optimization
- Use local models when possible
- Configure proxy settings if needed
- Optimize Docker networking

## Security Considerations

### API Key Management
- Store keys in environment variables
- Use `.env` files (add to `.gitignore`)
- Consider using secret management tools

### Sandbox Security
- Limit workspace access with `SANDBOX_VOLUMES`
- Use non-root user ID
- Monitor container permissions

## Integration Enhancements

### VS Code Integration
- Install OpenHands VS Code extension
- Configure workspace settings
- Set up keyboard shortcuts

### Git Integration
- Configure Git credentials in container
- Set up SSH keys for repository access
- Enable Git hooks for automation

### CI/CD Integration
- Use headless mode for automation
- Configure GitHub Actions
- Set up automated testing

## Next Steps

1. **Install and configure** using preferred method
2. **Test basic functionality** with simple tasks
3. **Initialize your repository** with `/init`
4. **Configure advanced settings** as needed
5. **Integrate with existing workflow**

## Support Resources

- [Official Documentation](https://docs.all-hands.dev)
- [GitHub Repository](https://github.com/All-Hands-AI/OpenHands)
- [Discord Community](https://discord.gg/ESHStjSjD4)
- [Slack Workspace](https://join.slack.com/t/openhands-ai/shared_invite/zt-34zm4j0gj-Qz5kRHoca8DFCbqXPS~f_A)
