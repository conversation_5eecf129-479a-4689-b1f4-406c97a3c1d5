# Official OpenHands Startup Script
# Following instructions from https://github.com/All-Hands-AI/OpenHands

param(
    [int]$Port = 3000
)

Write-Host "🦙 Starting OpenHands (Official Method)" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

# Check if Docker is running
Write-Host "🔍 Checking Docker..." -ForegroundColor Yellow
try {
    docker info | Out-Null
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop." -ForegroundColor Red
    exit 1
}

# Find available port
Write-Host "🔍 Checking port availability..." -ForegroundColor Yellow
function Test-PortAvailable {
    param([int]$TestPort)
    try {
        $listener = [System.Net.Sockets.TcpListener]::new([System.Net.IPAddress]::Any, $TestPort)
        $listener.Start()
        $listener.Stop()
        return $true
    } catch {
        return $false
    }
}

$originalPort = $Port
while (-not (Test-PortAvailable -TestPort $Port)) {
    Write-Host "❌ Port $Port is in use" -ForegroundColor Red
    $Port++
    if ($Port -gt ($originalPort + 20)) {
        Write-Host "❌ Could not find available port in range $originalPort-$($originalPort + 20)" -ForegroundColor Red
        exit 1
    }
}
Write-Host "✅ Using port $Port" -ForegroundColor Green

# Create OpenHands directory if it doesn't exist
$openhandsDir = "$env:USERPROFILE\.openhands"
if (-not (Test-Path $openhandsDir)) {
    New-Item -ItemType Directory -Path $openhandsDir -Force | Out-Null
    Write-Host "✅ Created OpenHands directory: $openhandsDir" -ForegroundColor Green
}

# Set environment variables for Ollama
$env:LLM_MODEL = "ollama/qwen2.5-coder:7b"
$env:LLM_BASE_URL = "http://************:11434"
$env:LLM_API_KEY = "ollama"

Write-Host "`n🔧 Configuration:" -ForegroundColor Cyan
Write-Host "  LLM Model: $env:LLM_MODEL" -ForegroundColor White
Write-Host "  LLM Base URL: $env:LLM_BASE_URL" -ForegroundColor White
Write-Host "  Port: $Port" -ForegroundColor White
Write-Host "  Data Directory: $openhandsDir" -ForegroundColor White

# Test Ollama connection
Write-Host "`n🧪 Testing Ollama connection..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$env:LLM_BASE_URL/api/tags" -Method GET -TimeoutSec 5
    $modelCount = $response.models.Count
    Write-Host "✅ Ollama accessible with $modelCount models" -ForegroundColor Green
    
    # Check if our model exists
    $modelExists = $response.models | Where-Object { $_.name -eq "qwen2.5-coder:7b" }
    if ($modelExists) {
        Write-Host "✅ Model 'qwen2.5-coder:7b' is available" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Model 'qwen2.5-coder:7b' not found on server" -ForegroundColor Yellow
        Write-Host "   Available models:" -ForegroundColor Gray
        $response.models | Select-Object -First 3 | ForEach-Object {
            Write-Host "   - $($_.name)" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "⚠️  Cannot connect to Ollama server" -ForegroundColor Yellow
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Gray
    Write-Host "   Continuing anyway..." -ForegroundColor Yellow
}

Write-Host "`n🚀 Starting OpenHands..." -ForegroundColor Green
Write-Host "🌐 Access OpenHands at: http://localhost:$Port" -ForegroundColor Cyan
Write-Host "🛑 Press Ctrl+C to stop" -ForegroundColor Yellow
Write-Host "=" * 50

# Official Docker command from GitHub repository
$dockerCmd = @"
docker run -it --rm --pull=always \
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.45-nikolaik \
    -e LOG_ALL_EVENTS=true \
    -e LLM_MODEL=$env:LLM_MODEL \
    -e LLM_BASE_URL=$env:LLM_BASE_URL \
    -e LLM_API_KEY=$env:LLM_API_KEY \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -v $($openhandsDir.Replace('\', '/')):/home/<USER>/.openhands \
    -p ${Port}:3000 \
    --add-host host.docker.internal:host-gateway \
    --name openhands-app \
    docker.all-hands.dev/all-hands-ai/openhands:0.45
"@

# Convert to PowerShell format
$dockerArgs = @(
    "run", "-it", "--rm", "--pull=always",
    "-e", "SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.45-nikolaik",
    "-e", "LOG_ALL_EVENTS=true",
    "-e", "LLM_MODEL=$env:LLM_MODEL",
    "-e", "LLM_BASE_URL=$env:LLM_BASE_URL", 
    "-e", "LLM_API_KEY=$env:LLM_API_KEY",
    "-v", "/var/run/docker.sock:/var/run/docker.sock",
    "-v", "$($openhandsDir.Replace('\', '/')):/home/<USER>/.openhands",
    "-p", "${Port}:3000",
    "--add-host", "host.docker.internal:host-gateway",
    "--name", "openhands-app",
    "docker.all-hands.dev/all-hands-ai/openhands:0.45"
)

try {
    # Start OpenHands using official Docker method
    & docker $dockerArgs
} catch {
    Write-Host "`n❌ Error starting OpenHands: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 Make sure Docker is running and try again" -ForegroundColor Yellow
}

Write-Host "`n🏁 OpenHands stopped" -ForegroundColor Gray
