@echo off
echo 🦙 Starting OpenHands with Docker (Official Method)
echo ================================================

REM Set environment variables for Ollama
set LLM_MODEL=ollama/qwen2.5-coder:7b
set LLM_BASE_URL=http://************:11434
set LLM_API_KEY=ollama

echo 🔧 Configuration:
echo   LLM_MODEL=%LLM_MODEL%
echo   LLM_BASE_URL=%LLM_BASE_URL%
echo   Port: 3000

echo.
echo 🧪 Testing Ollama connection...
curl -s %LLM_BASE_URL%/api/tags >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Ollama server is accessible
) else (
    echo ⚠️  Cannot connect to Ollama server
    echo    Continuing anyway...
)

echo.
echo 🚀 Starting OpenHands...
echo 🌐 Access at: http://localhost:3000
echo 🛑 Press Ctrl+C to stop
echo.

REM Create OpenHands data directory
if not exist "%USERPROFILE%\.openhands" mkdir "%USERPROFILE%\.openhands"

REM Official Docker command from GitHub
docker run -it --rm --pull=always ^
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.45-nikolaik ^
    -e LOG_ALL_EVENTS=true ^
    -e LLM_MODEL=%LLM_MODEL% ^
    -e LLM_BASE_URL=%LLM_BASE_URL% ^
    -e LLM_API_KEY=%LLM_API_KEY% ^
    -v /var/run/docker.sock:/var/run/docker.sock ^
    -v "%USERPROFILE%\.openhands":/home/<USER>/.openhands ^
    -p 3000:3000 ^
    --add-host host.docker.internal:host-gateway ^
    --name openhands-app ^
    docker.all-hands.dev/all-hands-ai/openhands:0.45

echo.
echo 🏁 OpenHands stopped
pause
