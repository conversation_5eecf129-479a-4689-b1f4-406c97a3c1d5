#!/usr/bin/env python3
"""
Simple OpenHands Server Startup Script
"""

import os
import sys
import uvicorn
from pathlib import Path

def setup_environment():
    """Setup environment variables for Ollama"""
    print("🔧 Setting up environment...")
    
    # Load from .env file if it exists
    env_file = Path("openhands/configs/.env")
    if env_file.exists():
        print(f"📋 Loading environment from: {env_file}")
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
    
    # Set essential environment variables
    env_vars = {
        'LLM_MODEL': 'ollama/qwen2.5-coder:7b',
        'LLM_BASE_URL': 'http://************:11434',
        'LLM_API_KEY': 'ollama',
        'WORKSPACE_DIR': './openhands/workspaces',
        'LOG_LEVEL': 'INFO',
        'PORT': '3000',
        'HOST': '0.0.0.0'
    }
    
    for key, value in env_vars.items():
        if key not in os.environ:
            os.environ[key] = value
        print(f"   {key}={os.environ[key]}")
    
    print("✅ Environment setup complete")

def start_server():
    """Start the OpenHands server"""
    print("🚀 Starting OpenHands server...")
    
    try:
        # Import the FastAPI app
        from openhands.server.app import app
        
        host = os.environ.get('HOST', '0.0.0.0')
        port = int(os.environ.get('PORT', 3000))
        
        print(f"🌐 Server will start on: http://{host}:{port}")
        print(f"🌐 Access OpenHands at: http://localhost:{port}")
        print("🛑 Press Ctrl+C to stop the server")
        print("=" * 50)
        
        # Start the server with uvicorn
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level="info",
            access_log=True
        )
        
    except ImportError as e:
        print(f"❌ Cannot import OpenHands app: {e}")
        print("💡 Trying alternative startup method...")
        
        try:
            # Try the listen module
            from openhands.server.listen import main
            main()
        except Exception as e2:
            print(f"❌ Alternative startup failed: {e2}")
            return False
    
    except Exception as e:
        print(f"❌ Server startup error: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("🦙 OpenHands Server Startup")
    print("===========================")
    
    # Setup environment
    setup_environment()
    
    # Test Ollama connection
    print("\n🔍 Testing Ollama connection...")
    try:
        import requests
        ollama_url = os.environ.get('LLM_BASE_URL')
        response = requests.get(f"{ollama_url}/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama server is accessible")
        else:
            print(f"⚠️  Ollama server returned status: {response.status_code}")
    except Exception as e:
        print(f"⚠️  Ollama connection test failed: {e}")
        print("   Continuing anyway...")
    
    # Start server
    print("\n" + "=" * 50)
    try:
        start_server()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        return 0
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
