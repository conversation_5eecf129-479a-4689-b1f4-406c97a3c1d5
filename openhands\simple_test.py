#!/usr/bin/env python3
"""
Simple test to verify OpenHands + Ollama integration
"""

import requests
import json
import os

def test_ollama_connection():
    """Test direct connection to Ollama"""
    print("🧪 Testing Ollama Connection")
    print("=" * 30)
    
    ollama_url = "http://192.168.1.26:11434"
    
    try:
        # Test 1: List models
        print("1️⃣  Testing model list...")
        response = requests.get(f"{ollama_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Found {len(models)} models")
            
            # Show first few models
            for i, model in enumerate(models[:5]):
                print(f"   - {model['name']}")
            if len(models) > 5:
                print(f"   ... and {len(models) - 5} more")
        else:
            print(f"❌ Failed to get models: {response.status_code}")
            return False
        
        # Test 2: Simple generation
        print("\n2️⃣  Testing model generation...")
        test_data = {
            "model": "qwen2.5-coder:7b",
            "prompt": "Write a simple Python function that adds two numbers.",
            "stream": False
        }
        
        print("   Sending request to model (this may take a moment)...")
        response = requests.post(
            f"{ollama_url}/api/generate",
            json=test_data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            if 'response' in result and result['response']:
                print("✅ Model generation successful!")
                print("📝 Response:")
                print("-" * 40)
                print(result['response'][:200] + "..." if len(result['response']) > 200 else result['response'])
                print("-" * 40)
                return True
            else:
                print("⚠️  Model responded but with empty response")
                return False
        else:
            print(f"❌ Generation failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out (this is normal for first request)")
        print("   The model may be loading. Try again in a moment.")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_openhands_import():
    """Test OpenHands import and basic functionality"""
    print("\n🧪 Testing OpenHands Import")
    print("=" * 30)
    
    try:
        import openhands
        print(f"✅ OpenHands imported successfully")
        print(f"   Version: {openhands.__version__}")
        
        # Test server module
        try:
            import openhands.server
            print("✅ Server module available")
            
            # Check for app
            try:
                from openhands.server.app import app
                print("✅ FastAPI app available")
                return True
            except Exception as e:
                print(f"⚠️  FastAPI app import failed: {e}")
                return False
                
        except Exception as e:
            print(f"❌ Server module import failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ OpenHands import failed: {e}")
        return False

def create_simple_config():
    """Create a simple configuration for testing"""
    print("\n🔧 Creating Test Configuration")
    print("=" * 30)
    
    config = {
        "LLM_MODEL": "ollama/qwen2.5-coder:7b",
        "LLM_BASE_URL": "http://192.168.1.26:11434",
        "LLM_API_KEY": "ollama",
        "WORKSPACE_DIR": "./openhands/workspaces",
        "LOG_LEVEL": "INFO"
    }
    
    # Set environment variables
    for key, value in config.items():
        os.environ[key] = value
        print(f"   {key}={value}")
    
    print("✅ Configuration set")
    return True

def main():
    """Main test function"""
    print("🦙 OpenHands + Ollama Integration Test")
    print("=" * 40)
    
    # Test 1: Ollama connection
    ollama_ok = test_ollama_connection()
    
    # Test 2: OpenHands import
    openhands_ok = test_openhands_import()
    
    # Test 3: Configuration
    config_ok = create_simple_config()
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 20)
    print(f"Ollama Connection: {'✅ PASS' if ollama_ok else '❌ FAIL'}")
    print(f"OpenHands Import:  {'✅ PASS' if openhands_ok else '❌ FAIL'}")
    print(f"Configuration:     {'✅ PASS' if config_ok else '❌ FAIL'}")
    
    if ollama_ok and openhands_ok and config_ok:
        print("\n🎉 All tests passed! Integration should work.")
        print("\n💡 Next steps:")
        print("1. Try starting OpenHands server manually")
        print("2. Check for any missing dependencies")
        print("3. Look for OpenHands documentation for your version")
    else:
        print("\n🔧 Some tests failed. Please address the issues above.")
    
    return 0 if (ollama_ok and openhands_ok and config_ok) else 1

if __name__ == "__main__":
    exit(main())
