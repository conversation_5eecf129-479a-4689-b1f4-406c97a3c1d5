#!/usr/bin/env python3
"""
Simple port checker and OpenHands starter
"""

import socket
import os
import sys
import time

def check_port(port):
    """Check if a port is available"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('localhost', port))
            return result != 0  # True if port is available
    except:
        return True  # Assume available if we can't check

def find_available_port(start=3000, end=3020):
    """Find first available port in range"""
    print(f"🔍 Checking ports {start}-{end}...")
    
    for port in range(start, end + 1):
        if check_port(port):
            print(f"✅ Port {port} is available")
            return port
        else:
            print(f"❌ Port {port} is in use")
    
    return None

def setup_environment(port):
    """Setup environment variables"""
    print("🔧 Setting up environment...")
    
    # Load from .env file if exists
    env_file = "openhands/configs/.env"
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
    
    # Set essential variables
    os.environ['PORT'] = str(port)
    os.environ['HOST'] = '0.0.0.0'
    os.environ['LLM_MODEL'] = 'ollama/qwen2.5-coder:7b'
    os.environ['LLM_BASE_URL'] = 'http://************:11434'
    os.environ['LLM_API_KEY'] = 'ollama'
    
    print(f"   PORT={port}")
    print(f"   LLM_MODEL={os.environ['LLM_MODEL']}")
    print(f"   LLM_BASE_URL={os.environ['LLM_BASE_URL']}")

def test_ollama():
    """Quick test of Ollama connection"""
    print("🧪 Testing Ollama connection...")
    try:
        import requests
        response = requests.get("http://************:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama accessible with {len(models)} models")
            return True
        else:
            print(f"⚠️  Ollama returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"⚠️  Ollama test failed: {e}")
        return False

def start_server(port):
    """Start OpenHands server"""
    print(f"🚀 Starting OpenHands on port {port}...")
    print(f"🌐 Access at: http://localhost:{port}")
    print("🛑 Press Ctrl+C to stop")
    print("=" * 50)
    
    try:
        # Try uvicorn approach
        import uvicorn
        from openhands.server.app import app
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=port,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Try: pip install uvicorn")
        return False
    except Exception as e:
        print(f"❌ Server error: {e}")
        return False

def main():
    print("🦙 OpenHands Port Check & Start")
    print("=" * 35)
    
    # Find available port
    port = find_available_port()
    if not port:
        print("❌ No available ports found")
        return 1
    
    # Setup environment
    setup_environment(port)
    
    # Test Ollama
    test_ollama()
    
    # Start server
    try:
        start_server(port)
    except KeyboardInterrupt:
        print("\n🛑 Stopped by user")
        return 0
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
