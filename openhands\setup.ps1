# OpenHands CLI Setup Script for Windows
# This script sets up OpenHands CLI with all dependencies and configurations

param(
    [switch]$SkipDependencies,
    [switch]$DevMode,
    [string]$LLMProvider = "openai"
)

Write-Host "🚀 OpenHands CLI Setup & Enhancement Script" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Warning "⚠️  Running without administrator privileges. Some features may not work correctly."
}

# Function to check if a command exists
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Function to install Python package with retry
function Install-PythonPackage {
    param($Package, $MaxRetries = 3)
    
    for ($i = 1; $i -le $MaxRetries; $i++) {
        Write-Host "📦 Installing $Package (attempt $i/$MaxRetries)..." -ForegroundColor Yellow
        try {
            pip install $Package --upgrade
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Successfully installed $Package" -ForegroundColor Green
                return $true
            }
        } catch {
            Write-Warning "❌ Failed to install $Package on attempt $i"
        }
        
        if ($i -lt $MaxRetries) {
            Write-Host "⏳ Waiting 5 seconds before retry..." -ForegroundColor Yellow
            Start-Sleep -Seconds 5
        }
    }
    
    Write-Error "❌ Failed to install $Package after $MaxRetries attempts"
    return $false
}

# Step 1: System Requirements Check
Write-Host "`n🔍 Step 1: System Requirements Check" -ForegroundColor Blue
Write-Host "=====================================" -ForegroundColor Blue

# Check Python
if (Test-Command python) {
    $pythonVersion = python --version
    Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
} else {
    Write-Error "❌ Python not found. Please install Python 3.11+ first."
    exit 1
}

# Check pip
if (Test-Command pip) {
    $pipVersion = pip --version
    Write-Host "✅ pip found: $pipVersion" -ForegroundColor Green
} else {
    Write-Error "❌ pip not found. Please install pip first."
    exit 1
}

# Check Docker
if (Test-Command docker) {
    $dockerVersion = docker --version
    Write-Host "✅ Docker found: $dockerVersion" -ForegroundColor Green
    
    # Check if Docker is running
    try {
        docker info | Out-Null
        Write-Host "✅ Docker is running" -ForegroundColor Green
    } catch {
        Write-Warning "⚠️  Docker is installed but not running. Please start Docker Desktop."
    }
} else {
    Write-Warning "⚠️  Docker not found. Some features may not work without Docker."
}

# Step 2: Core Installation
Write-Host "`n📦 Step 2: Core OpenHands Installation" -ForegroundColor Blue
Write-Host "======================================" -ForegroundColor Blue

if (-not $SkipDependencies) {
    # Install core package first
    Write-Host "Installing OpenHands core package..." -ForegroundColor Yellow
    Install-PythonPackage "openhands-ai"
    
    # Install essential dependencies in batches to avoid conflicts
    $essentialPackages = @(
        "docker",
        "aiohttp",
        "fastapi",
        "uvicorn",
        "python-dotenv",
        "pyyaml",
        "click",
        "rich",
        "psutil"
    )
    
    foreach ($package in $essentialPackages) {
        Install-PythonPackage $package
    }
    
    # Install LLM provider packages based on selection
    switch ($LLMProvider.ToLower()) {
        "openai" {
            Install-PythonPackage "openai"
        }
        "anthropic" {
            Install-PythonPackage "anthropic"
        }
        "azure" {
            Install-PythonPackage "openai"
            Install-PythonPackage "azure-identity"
        }
        "ollama" {
            Install-PythonPackage "ollama"
        }
        default {
            Write-Host "Installing OpenAI as default LLM provider..." -ForegroundColor Yellow
            Install-PythonPackage "openai"
        }
    }
} else {
    Write-Host "⏭️  Skipping dependency installation" -ForegroundColor Yellow
}

# Step 3: Directory Structure
Write-Host "`n📁 Step 3: Setting up directory structure" -ForegroundColor Blue
Write-Host "==========================================" -ForegroundColor Blue

$directories = @("workspaces", "logs", "configs", "backups")
foreach ($dir in $directories) {
    $fullPath = Join-Path $PWD "openhands\$dir"
    if (-not (Test-Path $fullPath)) {
        New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
        Write-Host "✅ Created directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "✅ Directory exists: $dir" -ForegroundColor Green
    }
}

# Step 4: Configuration Setup
Write-Host "`n⚙️  Step 4: Configuration Setup" -ForegroundColor Blue
Write-Host "===============================" -ForegroundColor Blue

# Copy template to .env if it doesn't exist
$envPath = "openhands\configs\.env"
$templatePath = "openhands\configs\.env.template"

if (-not (Test-Path $envPath) -and (Test-Path $templatePath)) {
    Copy-Item $templatePath $envPath
    Write-Host "✅ Created .env file from template" -ForegroundColor Green
    Write-Host "📝 Please edit openhands\configs\.env with your API keys" -ForegroundColor Yellow
} else {
    Write-Host "✅ Configuration files ready" -ForegroundColor Green
}

# Step 5: Verification
Write-Host "`n🧪 Step 5: Installation Verification" -ForegroundColor Blue
Write-Host "====================================" -ForegroundColor Blue

try {
    python -c "import openhands; print('✅ OpenHands import successful')"
    Write-Host "✅ OpenHands package verification passed" -ForegroundColor Green
} catch {
    Write-Warning "⚠️  OpenHands package verification failed"
}

# Step 6: Create helper scripts
Write-Host "`n🛠️  Step 6: Creating helper scripts" -ForegroundColor Blue
Write-Host "===================================" -ForegroundColor Blue

# Create start script
$startScript = @"
# OpenHands Start Script
Write-Host "🚀 Starting OpenHands CLI..." -ForegroundColor Cyan

# Load environment variables
if (Test-Path "openhands\configs\.env") {
    Get-Content "openhands\configs\.env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
    Write-Host "✅ Environment variables loaded" -ForegroundColor Green
} else {
    Write-Warning "⚠️  .env file not found. Please configure your environment."
}

# Start OpenHands
python -m openhands.cli `$args
"@

$startScript | Out-File -FilePath "openhands\start.ps1" -Encoding UTF8
Write-Host "✅ Created start.ps1 script" -ForegroundColor Green

# Create status script
$statusScript = @"
# OpenHands Status Check Script
Write-Host "🔍 OpenHands System Status" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan

# Check Python
try {
    `$pythonVersion = python --version
    Write-Host "✅ Python: `$pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python: Not available" -ForegroundColor Red
}

# Check OpenHands
try {
    python -c "import openhands; print('Available')" | Out-Null
    Write-Host "✅ OpenHands: Available" -ForegroundColor Green
} catch {
    Write-Host "❌ OpenHands: Not available" -ForegroundColor Red
}

# Check Docker
try {
    docker info | Out-Null
    Write-Host "✅ Docker: Running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker: Not running" -ForegroundColor Red
}

# Check directories
`$dirs = @("workspaces", "logs", "configs", "backups")
foreach (`$dir in `$dirs) {
    if (Test-Path "openhands\`$dir") {
        Write-Host "✅ Directory `$dir: Exists" -ForegroundColor Green
    } else {
        Write-Host "❌ Directory `$dir: Missing" -ForegroundColor Red
    }
}

# Check configuration
if (Test-Path "openhands\configs\.env") {
    Write-Host "✅ Configuration: .env file exists" -ForegroundColor Green
} else {
    Write-Host "⚠️  Configuration: .env file missing" -ForegroundColor Yellow
}
"@

$statusScript | Out-File -FilePath "openhands\status.ps1" -Encoding UTF8
Write-Host "✅ Created status.ps1 script" -ForegroundColor Green

# Final Summary
Write-Host "`n🎉 Setup Complete!" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green
Write-Host ""
Write-Host "📁 Directory structure created in: openhands\" -ForegroundColor Cyan
Write-Host "⚙️  Configuration files: openhands\configs\" -ForegroundColor Cyan
Write-Host "🚀 Start script: openhands\start.ps1" -ForegroundColor Cyan
Write-Host "🔍 Status script: openhands\status.ps1" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Edit openhands\configs\.env with your API keys" -ForegroundColor White
Write-Host "2. Run: .\openhands\status.ps1 to check system status" -ForegroundColor White
Write-Host "3. Run: .\openhands\start.ps1 --help to see available commands" -ForegroundColor White
Write-Host ""
Write-Host "For troubleshooting, check the logs in: openhands\logs\" -ForegroundColor Cyan
