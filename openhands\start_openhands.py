#!/usr/bin/env python3
"""
OpenHands Startup Script with Ollama Configuration
This script starts OpenHands with the proper Ollama configuration
"""

import os
import sys
from pathlib import Path

def load_env_file(env_path):
    """Load environment variables from .env file"""
    if not os.path.exists(env_path):
        print(f"❌ Environment file not found: {env_path}")
        return False
    
    print(f"📋 Loading environment from: {env_path}")
    with open(env_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key.strip()] = value.strip()
                if 'API_KEY' not in key:  # Don't print API keys
                    print(f"   {key.strip()}={value.strip()}")
    
    return True

def test_ollama_connection():
    """Test connection to Ollama server"""
    import requests
    
    ollama_url = os.environ.get('LLM_BASE_URL', 'http://************:11434')
    print(f"🔍 Testing Ollama connection to: {ollama_url}")
    
    try:
        response = requests.get(f"{ollama_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama server accessible with {len(models)} models")
            
            # Check if configured model exists
            configured_model = os.environ.get('LLM_MODEL', '').replace('ollama/', '')
            if configured_model:
                model_exists = any(m['name'] == configured_model for m in models)
                if model_exists:
                    print(f"✅ Configured model '{configured_model}' is available")
                else:
                    print(f"⚠️  Configured model '{configured_model}' not found")
                    print("Available models:")
                    for model in models[:5]:  # Show first 5 models
                        print(f"   - {model['name']}")
            return True
        else:
            print(f"❌ Ollama server returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to Ollama server: {e}")
        return False

def start_openhands_server():
    """Start OpenHands server"""
    print("🚀 Starting OpenHands server...")
    
    try:
        # Try different ways to start OpenHands
        from openhands.server.listen import main as server_main
        print("✅ Found OpenHands server module")
        
        # Set default port if not specified
        if 'PORT' not in os.environ:
            os.environ['PORT'] = '3000'
        
        print(f"🌐 Starting server on port {os.environ.get('PORT', '3000')}")
        print("📝 Access OpenHands at: http://localhost:3000")
        print("🛑 Press Ctrl+C to stop the server")
        
        # Start the server
        server_main()
        
    except ImportError as e:
        print(f"❌ Cannot import OpenHands server: {e}")
        print("💡 Try installing with: pip install openhands-ai --upgrade")
        return False
    except Exception as e:
        print(f"❌ Error starting OpenHands server: {e}")
        return False

def main():
    """Main function"""
    print("🦙 OpenHands + Ollama Startup")
    print("=============================")
    
    # Change to the script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir.parent)
    
    # Load environment configuration
    env_path = "openhands/configs/.env"
    if not load_env_file(env_path):
        return 1
    
    # Test Ollama connection
    if not test_ollama_connection():
        print("\n⚠️  Ollama connection failed, but continuing anyway...")
        print("   Make sure Ollama server is running and accessible")
    
    # Start OpenHands server
    try:
        start_openhands_server()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        return 0
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
